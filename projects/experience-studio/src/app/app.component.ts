import { Component, OnInit, ChangeDetectionStrategy, ChangeDetectorRef, OnDestroy } from '@angular/core';
import { Router, RouterOutlet, NavigationStart, NavigationEnd } from '@angular/router';
import { NavHeaderComponent } from './shared/components/nav-header/nav-header.component';
import { AuthService } from './services/auth.service';
import { filter, take, takeUntil } from 'rxjs/operators';
import { Subject } from 'rxjs';
import { createLogger } from './shared/utils';
import { NavigationCleanupService } from './shared/services/navigation-cleanup.service';

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [RouterOutlet, NavHeaderComponent],
  templateUrl: './app.component.html',
  styleUrl: './app.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AppComponent implements OnInit, OnDestroy {
  title = 'experienceStudio';
  userProfile: any;
  private logger = createLogger('AppComponent');
  private destroy$ = new Subject<void>();

  constructor(
    public authService: AuthService,
    private cdr: ChangeDetectorRef,
    private router: Router,
    private navigationCleanupService: NavigationCleanupService
  ) {}

  async ngOnInit() {
    this.logger.info('🚀 Experience Studio application starting');

    // Initialize navigation cleanup monitoring
    this.initializeNavigationMonitoring();

    try {
      // Wait for authentication state to be determined
      this.authService.userProfile$.subscribe(profile => {
        this.userProfile = profile;
        this.cdr.markForCheck(); // Trigger change detection for OnPush
      });
      await new Promise<void>(resolve => {
        this.authService.isAuthenticated$
          .pipe(
            filter(isAuthenticated => isAuthenticated !== undefined),
            take(1)
          )
          .subscribe(async isAuthenticated => {
            // Only trigger login if not authenticated and not already in a login flow
            if (!isAuthenticated && !this.authService.isLoginInProgressState()) {
              await this.authService.login();
            }
            resolve();
          });
      });
    } catch (error) {
      this.logger.error('Error during authentication:', error);
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    this.logger.info('🧹 App component destroyed');
  }

  /**
   * Initialize navigation monitoring for cleanup
   */
  private initializeNavigationMonitoring(): void {
    this.logger.info('🔄 Initializing navigation monitoring');

    // Track current route for cleanup purposes
    this.router.events
      .pipe(
        filter(event => event instanceof NavigationStart),
        takeUntil(this.destroy$)
      )
      .subscribe((event: NavigationStart) => {
        // Store the current route before navigation
        sessionStorage.setItem('lastRoute', this.router.url);
        this.logger.debug('📍 Navigation start:', { from: this.router.url, to: event.url });
      });

    this.router.events
      .pipe(
        filter(event => event instanceof NavigationEnd),
        takeUntil(this.destroy$)
      )
      .subscribe((event: NavigationEnd) => {
        this.logger.debug('✅ Navigation end:', { url: event.url });
      });
  }
}
