# Reusable Vertical Stepper Component

A fully configurable, production-ready vertical stepper component that follows Angular best practices and provides comprehensive customization options.

## 🚀 Features

- ✅ **Fully Configurable**: TypeScript interfaces for all inputs with type safety
- ✅ **Event-Driven Architecture**: Comprehensive event callbacks for step interactions
- ✅ **Backward Compatible**: Supports both demo mode and production data
- ✅ **Typewriter Animation Effects**: Configurable text typing animations
- ✅ **Step Collapse/Expand**: Interactive step management with smooth animations
- ✅ **Theme Support**: Light and dark theme compatibility
- ✅ **Timer Display**: Real-time timer display with customizable visibility
- ✅ **Retry Functionality**: Configurable retry mechanism for failed steps
- ✅ **Smooth Animations**: Multiple animation speed options (slow/normal/fast/instant)
- ✅ **Responsive Design**: Mobile-friendly responsive layout
- ✅ **Markdown Support**: Rich text content rendering with markdown
- ✅ **Loading Indicators**: Animated spinners for active steps
- ✅ **Status Icons**: Visual indicators (checkmarks, X icons, numbers)
- ✅ **Angular Best Practices**: OnChanges lifecycle, proper change detection, JSDoc comments

## 📖 Usage

### Basic Implementation (Demo Mode)

```typescript
import { StandaloneVerticalStepperComponent } from './standalone-vertical-stepper.component';

@Component({
  selector: 'app-my-component',
  standalone: true,
  imports: [StandaloneVerticalStepperComponent],
  template: `
    <app-standalone-vertical-stepper
      [theme]="'light'"
      [demoMode]="'completed'"
      [restartable]="true">
    </app-standalone-vertical-stepper>
  `
})
export class MyComponent {}
```

### Production Implementation (Custom Data)

```typescript
import {
  StandaloneVerticalStepperComponent,
  StepperItem,
  StepperConfig,
  StepClickEvent,
  StepStatusChangeEvent
} from './standalone-vertical-stepper.component';

@Component({
  selector: 'app-my-component',
  standalone: true,
  imports: [StandaloneVerticalStepperComponent],
  template: `
    <app-standalone-vertical-stepper
      [steps]="stepperData"
      [theme]="'light'"
      [status]="'IN_PROGRESS'"
      [config]="stepperConfig"
      [currentStepIndex]="2"
      [restartable]="true"
      (stepClick)="onStepClick($event)"
      (statusChange)="onStatusChange($event)"
      (restart)="onRestart()"
      (timerUpdate)="onTimerUpdate($event)">
    </app-standalone-vertical-stepper>
  `
})
export class MyComponent {
  stepperData: StepperItem[] = [
    {
      id: 'step-1',
      title: 'Initialize Project',
      description: 'Setting up the project structure and dependencies.',
      status: 'completed',
      completionTime: 15
    },
    {
      id: 'step-2',
      title: 'Configure Environment',
      description: 'Setting up development and production environments.',
      status: 'completed',
      completionTime: 8
    },
    {
      id: 'step-3',
      title: 'Generate Components',
      description: 'Creating reusable UI components...',
      status: 'active',
      timerActive: true,
      startTime: Date.now()
    },
    {
      id: 'step-4',
      title: 'Run Tests',
      description: 'Execute comprehensive test suite.',
      status: 'pending'
    }
  ];

  stepperConfig: Partial<StepperConfig> = {
    animationSpeed: 'normal',
    showTimers: true,
    showRetryButtons: true,
    enableCollapse: true,
    enableTypewriter: true
  };

  onStepClick(event: StepClickEvent): void {
    console.log('Step clicked:', event);
    // Handle step click logic
  }

  onStatusChange(event: StepStatusChangeEvent): void {
    console.log('Status changed:', event);
    // Handle status change logic
  }

  onRestart(): void {
    console.log('Stepper restarted');
    // Handle restart logic
  }

  onTimerUpdate(event: { stepIndex: number; elapsedTime: number }): void {
    console.log('Timer update:', event);
    // Handle timer updates
  }
}
```

## 🔧 Component API

### Input Properties

| Property | Type | Default | Description |
|----------|------|---------|-------------|
| `steps` | `StepperItem[]` | `[]` | Array of step data (for reusable mode) |
| `theme` | `StepperTheme` | `'light'` | Visual theme ('light' \| 'dark') |
| `status` | `StepperStatus` | `'PENDING'` | Overall stepper status |
| `config` | `Partial<StepperConfig>` | `{}` | Configuration options |
| `currentStepIndex` | `number` | `0` | Index of currently active step |
| `restartable` | `boolean` | `false` | Whether to show restart button |
| `demoMode` | `string` | `undefined` | Demo mode for backward compatibility |

### Output Events

| Event | Type | Description |
|-------|------|-------------|
| `stepClick` | `StepClickEvent` | Emitted when a step is clicked |
| `statusChange` | `StepStatusChangeEvent` | Emitted when step status changes |
| `restart` | `void` | Emitted when restart button is clicked |
| `timerUpdate` | `{stepIndex: number, elapsedTime: number}` | Emitted when timer updates |

### TypeScript Interfaces

#### StepperItem
```typescript
interface StepperItem {
  id?: string;                    // Unique identifier
  title: string;                  // Step title
  description: string;            // Step description (supports markdown)
  status: StepStatus;             // Current status
  collapsible?: boolean;          // Whether step can be collapsed
  collapsed?: boolean;            // Whether step is currently collapsed
  startTime?: number;             // Start time for timer (timestamp)
  elapsedTime?: number;           // Elapsed time in seconds
  completionTime?: number;        // Completion time in seconds
  timerActive?: boolean;          // Whether timer is active
  retryCount?: number;            // Number of retry attempts
  maxRetries?: number;            // Maximum allowed retries
  data?: any;                     // Custom data
}
```

#### StepperConfig
```typescript
interface StepperConfig {
  animationSpeed?: AnimationSpeed;      // 'slow' | 'normal' | 'fast' | 'instant'
  showTimers?: boolean;                 // Show/hide timers
  showRetryButtons?: boolean;           // Show/hide retry buttons
  enableCollapse?: boolean;             // Enable step collapse/expand
  autoCollapseCompleted?: boolean;      // Auto-collapse completed steps
  enableTypewriter?: boolean;           // Enable typewriter animations
  customClasses?: string[];             // Custom CSS classes
  showStepNumbers?: boolean;            // Show step numbers on pending steps
  showConnectingLines?: boolean;        // Show connecting lines between steps
}
```

#### Event Interfaces
```typescript
interface StepClickEvent {
  step: StepperItem;
  stepIndex: number;
  action: 'toggle' | 'retry';
}

interface StepStatusChangeEvent {
  step: StepperItem;
  stepIndex: number;
  previousStatus: StepStatus;
  newStatus: StepStatus;
}
```

## Demo

Access the interactive demo at: `/experience/(primary:stepper-demo)`

The demo includes:
- Theme switching (light/dark)
- Mode switching (completed/in-progress/failed)
- Restart button toggle
- Live preview of all features

## Key Differences from Original

| Aspect | Original Component | Standalone Component |
|--------|-------------------|---------------------|
| **Data Source** | API calls, polling, SSE | Hardcoded demo data |
| **Dependencies** | Multiple services (PollingService, SSEService, etc.) | None (self-contained) |
| **Business Logic** | Application-specific logic | Generic demo logic |
| **Configuration** | Complex inputs (projectId, jobId, useApi) | Simple demo inputs |
| **State Management** | External state management | Internal state only |
| **Error Handling** | Real error reporting | Simulated error scenarios |

## File Structure

```
standalone-vertical-stepper/
├── standalone-vertical-stepper.component.ts    # Main component logic
├── standalone-vertical-stepper.component.html  # Template
├── standalone-vertical-stepper.component.scss  # Styling
├── demo/
│   ├── stepper-demo.component.ts               # Demo component
│   ├── stepper-demo.component.html             # Demo template
│   └── stepper-demo.component.scss             # Demo styling
└── README.md                                   # This file
```

## Styling

The component maintains all original styling including:
- CSS custom properties for theming
- Smooth animations and transitions
- Responsive design patterns
- Accessibility considerations

### CSS Variables

The component uses CSS custom properties that can be overridden:

```scss
:host {
  --stepper-animation-duration: 0.5s;
  --stepper-animation-timing: cubic-bezier(0.25, 0.1, 0.25, 1);
  --stepper-line-animation-duration: 1.5s;
  --color-primary: #6b46c1;
  --color-primary-light: #9f7aea;
  // ... more variables
}
```

## Integration

To integrate this component into other projects:

1. Copy the `standalone-vertical-stepper` folder
2. Ensure `ngx-markdown` is installed: `npm install ngx-markdown`
3. Import the component where needed
4. Customize the hardcoded data as required
5. Adjust styling variables if needed

## Customization

To customize the hardcoded data, modify the methods in `standalone-vertical-stepper.component.ts`:
- `initializeCompletedDemo()`
- `initializeInProgressDemo()`
- `initializeFailedDemo()`

Each method defines the step data structure with titles, descriptions, and states.
