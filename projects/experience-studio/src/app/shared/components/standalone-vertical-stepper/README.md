# Standalone Vertical Stepper Component

A reusable, standalone vertical stepper component extracted from the original vertical stepper with hardcoded data for demonstration and reuse purposes.

## Features

- ✅ **Typewriter Animation Effects**: Smooth text typing animations for titles and descriptions
- ✅ **Step Collapse/Expand**: Interactive step collapsing with smooth animations
- ✅ **Theme Support**: Light and dark theme compatibility
- ✅ **Timer Display**: Real-time timer display for active steps
- ✅ **Retry Functionality**: Retry button for failed steps with visual feedback
- ✅ **Smooth Animations**: Blur-to-focus transitions and fluid animations
- ✅ **Responsive Design**: Works across different screen sizes
- ✅ **Markdown Support**: Rich text content rendering with markdown
- ✅ **Loading Indicators**: Animated spinners for active steps
- ✅ **Status Icons**: Visual indicators (checkmarks, X icons, numbers)

## Usage

### Basic Implementation

```typescript
import { StandaloneVerticalStepperComponent } from './standalone-vertical-stepper.component';

@Component({
  selector: 'app-my-component',
  standalone: true,
  imports: [StandaloneVerticalStepperComponent],
  template: `
    <app-standalone-vertical-stepper
      [theme]="'light'"
      [demoMode]="'completed'"
      [restartable]="true">
    </app-standalone-vertical-stepper>
  `
})
export class MyComponent {}
```

### Component Inputs

| Input | Type | Default | Description |
|-------|------|---------|-------------|
| `theme` | `'light' \| 'dark'` | `'light'` | Visual theme of the stepper |
| `demoMode` | `'completed' \| 'in-progress' \| 'failed'` | `'completed'` | Demo scenario to display |
| `restartable` | `boolean` | `false` | Whether to show the restart button |

### Demo Modes

#### Completed Mode
Shows a completed stepper with multiple finished steps, demonstrating:
- Completed step indicators (checkmarks)
- Collapsible step descriptions
- Timer completion display

#### In-Progress Mode
Shows an active stepper with a step currently processing, demonstrating:
- Loading spinner animation
- Active step highlighting
- Real-time timer updates
- Typewriter text animation

#### Failed Mode
Shows a failed stepper with error state, demonstrating:
- Error indicators (X icons)
- Retry button functionality
- Error message display
- Failed step styling

## Demo

Access the interactive demo at: `/experience/(primary:stepper-demo)`

The demo includes:
- Theme switching (light/dark)
- Mode switching (completed/in-progress/failed)
- Restart button toggle
- Live preview of all features

## Key Differences from Original

| Aspect | Original Component | Standalone Component |
|--------|-------------------|---------------------|
| **Data Source** | API calls, polling, SSE | Hardcoded demo data |
| **Dependencies** | Multiple services (PollingService, SSEService, etc.) | None (self-contained) |
| **Business Logic** | Application-specific logic | Generic demo logic |
| **Configuration** | Complex inputs (projectId, jobId, useApi) | Simple demo inputs |
| **State Management** | External state management | Internal state only |
| **Error Handling** | Real error reporting | Simulated error scenarios |

## File Structure

```
standalone-vertical-stepper/
├── standalone-vertical-stepper.component.ts    # Main component logic
├── standalone-vertical-stepper.component.html  # Template
├── standalone-vertical-stepper.component.scss  # Styling
├── demo/
│   ├── stepper-demo.component.ts               # Demo component
│   ├── stepper-demo.component.html             # Demo template
│   └── stepper-demo.component.scss             # Demo styling
└── README.md                                   # This file
```

## Styling

The component maintains all original styling including:
- CSS custom properties for theming
- Smooth animations and transitions
- Responsive design patterns
- Accessibility considerations

### CSS Variables

The component uses CSS custom properties that can be overridden:

```scss
:host {
  --stepper-animation-duration: 0.5s;
  --stepper-animation-timing: cubic-bezier(0.25, 0.1, 0.25, 1);
  --stepper-line-animation-duration: 1.5s;
  --color-primary: #6b46c1;
  --color-primary-light: #9f7aea;
  // ... more variables
}
```

## Integration

To integrate this component into other projects:

1. Copy the `standalone-vertical-stepper` folder
2. Ensure `ngx-markdown` is installed: `npm install ngx-markdown`
3. Import the component where needed
4. Customize the hardcoded data as required
5. Adjust styling variables if needed

## Customization

To customize the hardcoded data, modify the methods in `standalone-vertical-stepper.component.ts`:
- `initializeCompletedDemo()`
- `initializeInProgressDemo()`
- `initializeFailedDemo()`

Each method defines the step data structure with titles, descriptions, and states.
