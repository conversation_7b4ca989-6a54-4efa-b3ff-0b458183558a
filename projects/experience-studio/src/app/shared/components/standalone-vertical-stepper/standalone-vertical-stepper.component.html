<div class="vertical-stepper" [ngClass]="theme">
  <div class="stepper-container">
    @for (step of steps; track $index; let i = $index; let isLast = $last) {
      <div class="stepper-item" [ngClass]="getStepStatus(i)"
        [class.hidden]="!shouldShowStep(i)" [class.collapsed]="isStepCollapsed(i)"
        [class.in-progress-mode]="status === 'IN_PROGRESS' && isProcessingStep(i)">
        <!-- Connector Line -->
        @if (!isLast) {
          <div class="step-line-container" [class.hidden-line]="isStepCollapsed(i) || shouldHideStepLine(i)">
            <div class="step-line" [class.completed]="getStepStatus(i) === 'completed'"
              [class.animating]="isLineAnimating(i)" [class.expanding]="!isStepCollapsed(i)"></div>
          </div>
        }

        <!-- Circle -->
        <div class="step-circle" [class.active]="getStepStatus(i) === 'active' && !step.completed && !isFailureStep(step)"
          [class.failed]="(status === 'FAILED' && (i === currentStepIndex || step.title === 'Build Failed')) || isFailureStep(step) || step.title === this.getDisplayTitleForProgress(StandaloneStepperState.BUILD_FAILED)"
          [class.processing]="status === 'IN_PROGRESS' && currentStepIndex >= 0 && steps[currentStepIndex] && !steps[currentStepIndex].completed && !isFailureStep(step) && step.title !== this.getDisplayTitleForProgress(StandaloneStepperState.BUILD_FAILED)"
          [class.clickable]="canStepBeCollapsed(i)"
          [class.non-collapsible]="!canStepBeCollapsed(i)"
          (click)="toggleStepCollapse(i)">
          <!-- Checkmark icon for completed steps -->
          @if ((getStepStatus(i) === 'completed' || step.completed) && !isFailureStep(step) && step.title !== getDisplayTitleForProgress(StandaloneStepperState.BUILD_FAILED)) {
            <svg class="step-icon"
              width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M0 12C0 5.37258 5.37258 0 12 0C18.6274 0 24 5.37258 24 12C24 18.6274 18.6274 24 12 24C5.37258 24 0 18.6274 0 12Z"
              fill="url(#paint0_linear_tick)" />
            <mask id="mask0_tick" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="24" height="24">
              <rect width="24" height="24" fill="#D9D9D9" />
            </mask>
            <g mask="url(#mask0_tick)">
              <path
                d="M10.5 15.75L6.75 12L8.16 10.59L10.5 12.93L15.84 7.59L17.25 9L10.5 15.75Z"
                fill="white" />
            </g>
            <defs>
              <linearGradient id="paint0_linear_tick" x1="0" y1="0" x2="24" y2="24" gradientUnits="userSpaceOnUse">
                <stop stop-color="#9c27b0" />
                <stop offset="1" stop-color="#e91e63" />
              </linearGradient>
            </defs>
            </svg>
          }

          <!-- X icon for failed steps -->
          @if (isFailureStep(step) || (status === 'FAILED' && i === currentStepIndex) || step.title === getDisplayTitleForProgress(StandaloneStepperState.BUILD_FAILED)) {
            <svg class="step-icon"
              width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M0 12C0 5.37258 5.37258 0 12 0C18.6274 0 24 5.37258 24 12C24 18.6274 18.6274 24 12 24C5.37258 24 0 18.6274 0 12Z"
              fill="url(#paint0_linear_x)" />
            <mask id="mask0_x" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="24" height="24">
              <rect width="24" height="24" fill="#D9D9D9" />
            </mask>
            <g mask="url(#mask0_x)">
              <path
                d="M18.3 5.71C17.91 5.32 17.28 5.32 16.89 5.71L12 10.59L7.11 5.7C6.72 5.31 6.09 5.31 5.7 5.7C5.31 6.09 5.31 6.72 5.7 7.11L10.59 12L5.7 16.89C5.31 17.28 5.31 17.91 5.7 18.3C6.09 18.69 6.72 18.69 7.11 18.3L12 13.41L16.89 18.3C17.28 18.69 17.91 18.69 18.3 18.3C18.69 17.91 18.69 17.28 18.3 16.89L13.41 12L18.3 7.11C18.68 6.73 18.68 6.09 18.3 5.71Z"
                fill="white" />
            </g>
            <defs>
              <linearGradient id="paint0_linear_x" x1="0" y1="0" x2="24" y2="24" gradientUnits="userSpaceOnUse">
                <stop stop-color="#f44336" />
                <stop offset="1" stop-color="#d32f2f" />
              </linearGradient>
            </defs>
            </svg>
          }

          <!-- Loading spinner for active steps -->
          @if (getStepStatus(i) === 'active' && !step.completed && !isFailureStep(step)) {
            <div class="modern-loading-spinner">
              <div class="spinner-ring"></div>
              <div class="spinner-ring"></div>
              <div class="spinner-ring"></div>
            </div>
          }

          <!-- Step number for pending steps -->
          @if (getStepStatus(i) !== 'completed' && getStepStatus(i) !== 'active' && !isFailureStep(step)) {
            <span class="step-number">{{ i + 1 }}</span>
          }
        </div>

        <!-- Content -->
        <div class="step-content">
          <!-- Content for completed or active steps only - next steps are completely hidden -->
          <div class="step-content-inner">
            <!-- Title is always visible and clickable -->
            <h3 class="step-title" [class.clickable]="true"
              [class.typing]="step.isTitleTyping"
              (click)="toggleStepCollapse(i)">
              <span class="step-title-text">{{ formatTitle(step.visibleTitle || step.title) }}</span>

              <!-- Timer display at the right end of the title -->
              @if ((step.timerActive && step.elapsedTime !== undefined) || (step.completionTime !== undefined)) {
                <div class="step-timer" [class.completed]="step.completionTime !== undefined && !step.timerActive">
                  {{ formatElapsedTime(step.completionTime || step.elapsedTime || 0) }}
                </div>
              }

              <!-- Retry button for failed steps -->
              @if (shouldShowRetryButton(step, i)) {
                <button class="step-retry-button" (click)="onRetryClick(i, $event)"
                  [title]="'Retry attempt ' + (step.retryCount || 0) + '/3'">
                <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M17.65 6.35C16.2 4.9 14.21 4 12 4C7.58 4 4.01 7.58 4.01 12C4.01 16.42 7.58 20 12 20C15.73 20 18.84 17.45 19.73 14H17.65C16.83 16.33 14.61 18 12 18C8.69 18 6 15.31 6 12C6 8.69 8.69 6 12 6C13.66 6 15.14 6.69 16.22 7.78L13 11H20V4L17.65 6.35Z"
                    fill="currentColor" />
                </svg>
                </button>
              }
            </h3>
            <!-- Description collapses independently -->
            <div class="step-description"
              [class.collapsed]="isStepCollapsed(i)"
              [class.expanding]="isStepExpanding(i)"
              [class.expanded]="isStepExpanded(i)"
              [class.collapsing]="isStepCollapsing(i)"
              [class.typing]="step.isTyping"
              [class.shimmer]="step.isRetrying">
              <markdown [data]="getSanitizedDescription(step.visibleDescription || step.description)"></markdown>
            </div>
          </div>
        </div>
      </div>
    }
  </div>

  <!-- Restart button (only shown when restartable is true) -->
  @if (restartable) {
    <div class="restart-button-container">
      <button class="restart-button" (click)="restartStepper()">Restart Process</button>
    </div>
  }
</div>
