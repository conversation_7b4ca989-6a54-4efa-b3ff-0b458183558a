import { CommonModule } from '@angular/common';
import {
  Component,
  Input,
  OnInit,
  OnDestroy,
  ChangeDetectorRef,
  ChangeDetectionStrategy,
} from '@angular/core';
import { MarkdownModule } from 'ngx-markdown';

// Standalone interfaces for the component
export interface StandaloneStepperItem {
  title: string;
  description: string;
  visibleTitle: string;
  visibleDescription: string;
  completed: boolean;
  active: boolean;
  collapsed?: boolean;
  isTyping?: boolean;
  isTitleTyping?: boolean;
  retryCount?: number;
  isRetrying?: boolean;
  startTime?: number;
  elapsedTime?: number;
  completionTime?: number;
  timerActive?: boolean;
}

export enum StandaloneStepperState {
  OVERVIEW = 'OVERVIEW',
  SEED_PROJECT_INITIALIZED = 'SEED_PROJECT_INITIALIZED',
  FILE_QUEUE = 'FILE_QUEUE',
  DESIGN_SYSTEM_MAPPED = 'DESIGN_SYSTEM_MAPPED',
  COMPONENTS_CREATED = 'COMPONENTS_CREATED',
  LAYOUT_ANALYZED = 'LAYOUT_ANALYZED',
  PAGES_GENERATED = 'PAGES_GENERATED',
  BUILD_STARTED = 'BUILD_STARTED',
  BUILD_SUCCEEDED = 'BUILD_SUCCEEDED',
  BUILD_FAILED = 'BUILD_FAILED',
  BUILD = 'BUILD',
  FILES_GENERATED = 'FILES_GENERATED',
  DEPLOY = 'DEPLOY',
  DEPLOYED = 'DEPLOYED',
}

export const StandaloneStepperStateDisplayTitles: Record<StandaloneStepperState, string> = {
  [StandaloneStepperState.OVERVIEW]: 'Project Overview',
  [StandaloneStepperState.SEED_PROJECT_INITIALIZED]: 'Seed Project',
  [StandaloneStepperState.FILE_QUEUE]: 'Components Identified',
  [StandaloneStepperState.DESIGN_SYSTEM_MAPPED]: 'Design System',
  [StandaloneStepperState.COMPONENTS_CREATED]: 'Components Created',
  [StandaloneStepperState.LAYOUT_ANALYZED]: 'Layout Identified',
  [StandaloneStepperState.PAGES_GENERATED]: 'Pages Created',
  [StandaloneStepperState.BUILD_STARTED]: 'Build Started',
  [StandaloneStepperState.BUILD_SUCCEEDED]: 'Build Completed',
  [StandaloneStepperState.BUILD_FAILED]: 'Build Failed',
  [StandaloneStepperState.BUILD]: 'Building Application',
  [StandaloneStepperState.FILES_GENERATED]: 'Files Generated',
  [StandaloneStepperState.DEPLOY]: 'Deploying Application',
  [StandaloneStepperState.DEPLOYED]: 'Deployed',
};

@Component({
  selector: 'app-standalone-vertical-stepper',
  standalone: true,
  imports: [CommonModule, MarkdownModule],
  templateUrl: './standalone-vertical-stepper.component.html',
  styleUrls: ['./standalone-vertical-stepper.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class StandaloneVerticalStepperComponent implements OnInit, OnDestroy {
  @Input() theme: 'light' | 'dark' = 'light';
  @Input() restartable: boolean = false;
  @Input() demoMode: 'completed' | 'in-progress' | 'failed' = 'completed';

  steps: StandaloneStepperItem[] = [];
  currentStep: StandaloneStepperItem | null = null;
  currentStepIndex: number = 0;
  animatingLine: boolean = false;
  status: string = 'COMPLETED';

  private timeoutRefs: { [key: string]: any } = {};
  private typingSpeed: number = 3;
  private userExpandedStep: number | null = null;
  private timerInterval: any;
  private timerUpdateInterval: number = 1000;
  private collapsedSteps: Set<number> = new Set();
  private expandingSteps: Set<number> = new Set();
  private collapsingSteps: Set<number> = new Set();
  private readonly ANIMATION_DURATION_MS = 500;

  // Expose enum to template
  public StandaloneStepperState = StandaloneStepperState;

  constructor(private cdr: ChangeDetectorRef) {}

  ngOnInit(): void {
    this.initializeHardcodedData();
    this.startInitialAnimations();
  }

  ngOnDestroy(): void {
    this.clearAllTimeouts();
    this.stopTimer();
  }

  private initializeHardcodedData(): void {
    switch (this.demoMode) {
      case 'completed':
        this.initializeCompletedDemo();
        break;
      case 'in-progress':
        this.initializeInProgressDemo();
        break;
      case 'failed':
        this.initializeFailedDemo();
        break;
    }
  }

  private initializeCompletedDemo(): void {
    this.status = 'COMPLETED';
    this.steps = [
      {
        title: 'Project Overview',
        description: '**Project Analysis Complete**\n\nSuccessfully analyzed the uploaded design and identified key components:\n- Navigation header with logo and menu items\n- Hero section with call-to-action\n- Feature cards layout\n- Footer with contact information\n\nReady to proceed with component generation.',
        visibleTitle: 'Project Overview',
        visibleDescription: '**Project Analysis Complete**\n\nSuccessfully analyzed the uploaded design and identified key components:\n- Navigation header with logo and menu items\n- Hero section with call-to-action\n- Feature cards layout\n- Footer with contact information\n\nReady to proceed with component generation.',
        completed: true,
        active: false,
        collapsed: true,
        completionTime: 15,
        timerActive: false
      },
      {
        title: 'Components Identified',
        description: '**Component Structure Mapped**\n\nIdentified and catalogued all UI components:\n\n- **Header Component**: Navigation with responsive menu\n- **Hero Component**: Main banner with CTA button\n- **Card Component**: Reusable feature cards\n- **Footer Component**: Contact and social links\n\nComponent hierarchy and dependencies established.',
        visibleTitle: 'Components Identified',
        visibleDescription: '**Component Structure Mapped**\n\nIdentified and catalogued all UI components:\n\n- **Header Component**: Navigation with responsive menu\n- **Hero Component**: Main banner with CTA button\n- **Card Component**: Reusable feature cards\n- **Footer Component**: Contact and social links\n\nComponent hierarchy and dependencies established.',
        completed: true,
        active: false,
        collapsed: true,
        completionTime: 23,
        timerActive: false
      }
    ];
  }

  private initializeInProgressDemo(): void {
    this.status = 'IN_PROGRESS';
    this.steps = [
      {
        title: 'Project Overview',
        description: '**Project Analysis Complete**\n\nSuccessfully analyzed the uploaded design and identified key components.',
        visibleTitle: 'Project Overview',
        visibleDescription: '**Project Analysis Complete**\n\nSuccessfully analyzed the uploaded design and identified key components.',
        completed: true,
        active: false,
        collapsed: true,
        completionTime: 15,
        timerActive: false
      },
      {
        title: 'Building Application',
        description: '**Build Process Started**\n\nCurrently compiling and building your application:\n\n- Installing dependencies...\n- Compiling TypeScript files...\n- Processing styles and assets...\n- Running build optimizations...\n\nThis may take a few moments.',
        visibleTitle: 'Building Application',
        visibleDescription: '',
        completed: false,
        active: true,
        collapsed: false,
        isTyping: true,
        isTitleTyping: false,
        elapsedTime: 0,
        timerActive: true,
        startTime: Date.now()
      }
    ];
    this.currentStepIndex = 1;
    this.currentStep = this.steps[1];
  }

  private initializeFailedDemo(): void {
    this.status = 'FAILED';
    this.steps = [
      {
        title: 'Project Overview',
        description: '**Project Analysis Complete**\n\nSuccessfully analyzed the uploaded design.',
        visibleTitle: 'Project Overview',
        visibleDescription: '**Project Analysis Complete**\n\nSuccessfully analyzed the uploaded design.',
        completed: true,
        active: false,
        collapsed: true,
        completionTime: 15,
        timerActive: false
      },
      {
        title: 'Build Failed',
        description: '**Build Process Failed**\n\n❌ **Error Details:**\n- TypeScript compilation errors detected\n- Missing dependency: @types/react\n- Syntax error in component file\n\n**Suggested Actions:**\n- Check component syntax\n- Install missing dependencies\n- Review TypeScript configuration\n\nClick retry to attempt build again.',
        visibleTitle: 'Build Failed',
        visibleDescription: '**Build Process Failed**\n\n❌ **Error Details:**\n- TypeScript compilation errors detected\n- Missing dependency: @types/react\n- Syntax error in component file\n\n**Suggested Actions:**\n- Check component syntax\n- Install missing dependencies\n- Review TypeScript configuration\n\nClick retry to attempt build again.',
        completed: false,
        active: true,
        collapsed: false,
        retryCount: 1,
        timerActive: false
      }
    ];
    this.currentStepIndex = 1;
    this.currentStep = this.steps[1];
  }

  private startInitialAnimations(): void {
    if (this.steps.length > 0) {
      this.currentStep = this.steps[0];
      this.currentStepIndex = 0;

      if (this.status === 'IN_PROGRESS') {
        this.collapsedSteps.delete(this.currentStepIndex);
      }

      setTimeout(() => {
        this.startTypewriterAnimation(this.currentStepIndex);
      }, 100);

      this.startTimer();
    }
  }

  // Animation and interaction methods
  startTypewriterAnimation(stepIndex: number): void {
    if (stepIndex < 0 || stepIndex >= this.steps.length) return;

    const step = this.steps[stepIndex];
    if (!step || step.isTyping || step.isTitleTyping) return;

    // Start title typing
    step.isTitleTyping = true;
    step.visibleTitle = '';
    this.typeText(step, 'title', stepIndex);
  }

  private typeText(step: StandaloneStepperItem, type: 'title' | 'description', stepIndex: number): void {
    const sourceText = type === 'title' ? step.title : step.description;
    const currentText = type === 'title' ? step.visibleTitle : step.visibleDescription;

    if (currentText.length < sourceText.length) {
      const nextChar = sourceText.charAt(currentText.length);

      if (type === 'title') {
        step.visibleTitle += nextChar;
      } else {
        step.visibleDescription += nextChar;
      }

      this.cdr.markForCheck();

      this.timeoutRefs[`typing-${type}-${stepIndex}`] = setTimeout(() => {
        this.typeText(step, type, stepIndex);
      }, this.typingSpeed);
    } else {
      // Typing complete
      if (type === 'title') {
        step.isTitleTyping = false;
        // Start description typing after title is complete
        setTimeout(() => {
          step.isTyping = true;
          step.visibleDescription = '';
          this.typeText(step, 'description', stepIndex);
        }, 200);
      } else {
        step.isTyping = false;
      }
      this.cdr.markForCheck();
    }
  }

  // Step status and interaction methods
  getStepStatus(index: number): string {
    if (index >= this.steps.length) return '';

    const step = this.steps[index];
    if (step.completed) return 'completed';
    if (step.active) return 'active';
    return 'pending';
  }

  shouldShowStep(index: number): boolean {
    return index < this.steps.length;
  }

  isStepCollapsed(index: number): boolean {
    return this.collapsedSteps.has(index);
  }

  isStepExpanding(index: number): boolean {
    return this.expandingSteps.has(index);
  }

  isStepExpanded(index: number): boolean {
    return !this.collapsedSteps.has(index) && !this.expandingSteps.has(index) && !this.collapsingSteps.has(index);
  }

  isStepCollapsing(index: number): boolean {
    return this.collapsingSteps.has(index);
  }

  shouldHideStepLine(index: number): boolean {
    return index >= this.steps.length - 1;
  }

  isLineAnimating(index: number): boolean {
    return this.animatingLine && index === this.currentStepIndex - 1;
  }

  isProcessingStep(index: number): boolean {
    return this.status === 'IN_PROGRESS' && index === this.currentStepIndex;
  }

  canStepBeCollapsed(index: number): boolean {
    // Allow collapsing of completed steps and failed steps
    return this.steps[index]?.completed || this.isFailureStep(this.steps[index]);
  }

  isFailureStep(step: StandaloneStepperItem): boolean {
    return step.title.toLowerCase().includes('failed') || step.retryCount !== undefined;
  }

  toggleStepCollapse(index: number): void {
    if (!this.canStepBeCollapsed(index)) return;

    const isCurrentlyCollapsed = this.collapsedSteps.has(index);

    if (isCurrentlyCollapsed) {
      // Expand step
      this.expandingSteps.add(index);
      this.collapsedSteps.delete(index);

      // Collapse other user-expanded steps (except processing step)
      if (this.userExpandedStep !== null && this.userExpandedStep !== index) {
        this.collapsedSteps.add(this.userExpandedStep);
      }
      this.userExpandedStep = index;

      setTimeout(() => {
        this.expandingSteps.delete(index);
        this.cdr.markForCheck();
      }, this.ANIMATION_DURATION_MS);
    } else {
      // Collapse step
      this.collapsingSteps.add(index);

      setTimeout(() => {
        this.collapsedSteps.add(index);
        this.collapsingSteps.delete(index);
        if (this.userExpandedStep === index) {
          this.userExpandedStep = null;
        }
        this.cdr.markForCheck();
      }, this.ANIMATION_DURATION_MS);
    }

    this.cdr.markForCheck();
  }

  // Timer functionality
  startTimer(): void {
    this.stopTimer();

    this.timerInterval = setInterval(() => {
      this.updateTimers();
    }, this.timerUpdateInterval);
  }

  stopTimer(): void {
    if (this.timerInterval) {
      clearInterval(this.timerInterval);
      this.timerInterval = null;
    }
  }

  private updateTimers(): void {
    let hasActiveTimer = false;

    this.steps.forEach(step => {
      if (step.timerActive && step.startTime) {
        step.elapsedTime = Math.floor((Date.now() - step.startTime) / 1000);
        hasActiveTimer = true;
      }
    });

    if (hasActiveTimer) {
      this.cdr.markForCheck();
    }
  }

  formatElapsedTime(seconds: number): string {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  }

  // Utility methods
  formatTitle(title: string): string {
    return title;
  }

  getSanitizedDescription(description: string): string {
    return description;
  }

  shouldShowRetryButton(step: StandaloneStepperItem, index: number): boolean {
    return this.isFailureStep(step) && this.status === 'FAILED';
  }

  onRetryClick(index: number, event?: Event): void {
    if (event) {
      event.stopPropagation();
    }

    const step = this.steps[index];
    if (step && step.retryCount !== undefined) {
      step.retryCount++;
      step.isRetrying = true;

      // Simulate retry process
      setTimeout(() => {
        step.isRetrying = false;
        // For demo purposes, just update the description
        step.description = `**Retry Attempt ${step.retryCount}**\n\nRetrying build process...\n\nPlease wait while we attempt to resolve the previous issues.`;
        step.visibleDescription = step.description;
        this.cdr.markForCheck();
      }, 2000);
    }
  }

  restartStepper(): void {
    this.clearAllTimeouts();
    this.stopTimer();
    this.initializeHardcodedData();
    this.startInitialAnimations();
  }

  private clearAllTimeouts(): void {
    Object.values(this.timeoutRefs).forEach(timeoutId => clearTimeout(timeoutId));
    this.timeoutRefs = {};
  }

  getDisplayTitleForProgress(state: StandaloneStepperState): string {
    return StandaloneStepperStateDisplayTitles[state];
  }
}
