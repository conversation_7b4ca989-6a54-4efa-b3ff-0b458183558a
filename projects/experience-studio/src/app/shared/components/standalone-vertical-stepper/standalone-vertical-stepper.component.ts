import { CommonModule } from '@angular/common';
import {
  Component,
  Input,
  Output,
  EventEmitter,
  OnInit,
  OnDestroy,
  OnChanges,
  SimpleChanges,
  ChangeDetectorRef,
  ChangeDetectionStrategy,
} from '@angular/core';
import { MarkdownModule } from 'ngx-markdown';

/**
 * Represents the status of a stepper step
 */
export type StepStatus = 'pending' | 'active' | 'completed' | 'failed';

/**
 * Represents the overall status of the stepper process
 */
export type StepperStatus = 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'FAILED';

/**
 * Theme options for the stepper component
 */
export type StepperTheme = 'light' | 'dark';

/**
 * Animation speed options for typewriter effects
 */
export type AnimationSpeed = 'slow' | 'normal' | 'fast' | 'instant';

/**
 * Configuration interface for step data
 * @interface StepperItem
 */
export interface StepperItem {
  /** Unique identifier for the step */
  id?: string;
  /** The main title of the step */
  title: string;
  /** Detailed description content (supports markdown) */
  description: string;
  /** Current status of the step */
  status: StepStatus;
  /** Whether the step can be collapsed/expanded */
  collapsible?: boolean;
  /** Whether the step is currently collapsed */
  collapsed?: boolean;
  /** Start time for timer calculation (timestamp) */
  startTime?: number;
  /** Elapsed time in seconds */
  elapsedTime?: number;
  /** Completion time in seconds */
  completionTime?: number;
  /** Whether the timer is currently active */
  timerActive?: boolean;
  /** Number of retry attempts for failed steps */
  retryCount?: number;
  /** Maximum allowed retry attempts */
  maxRetries?: number;
  /** Custom data that can be attached to the step */
  data?: any;
}

/**
 * Internal step item interface with additional runtime properties
 * @interface InternalStepperItem
 */
export interface InternalStepperItem extends StepperItem {
  /** Visible portion of title for typewriter effect */
  visibleTitle: string;
  /** Visible portion of description for typewriter effect */
  visibleDescription: string;
  /** Whether the step is currently active */
  active: boolean;
  /** Whether the step is completed */
  completed: boolean;
  /** Whether title is currently being typed */
  isTitleTyping?: boolean;
  /** Whether description is currently being typed */
  isTyping?: boolean;
  /** Whether the step is currently being retried */
  isRetrying?: boolean;
}

/**
 * Configuration options for the stepper component
 * @interface StepperConfig
 */
export interface StepperConfig {
  /** Animation speed for typewriter effects */
  animationSpeed?: AnimationSpeed;
  /** Whether to show timers on steps */
  showTimers?: boolean;
  /** Whether to show retry buttons on failed steps */
  showRetryButtons?: boolean;
  /** Whether to enable step collapse/expand functionality */
  enableCollapse?: boolean;
  /** Whether to auto-collapse completed steps */
  autoCollapseCompleted?: boolean;
  /** Whether to enable typewriter animations */
  enableTypewriter?: boolean;
  /** Custom CSS classes to apply to the component */
  customClasses?: string[];
  /** Whether to show step numbers on pending steps */
  showStepNumbers?: boolean;
  /** Whether to show connecting lines between steps */
  showConnectingLines?: boolean;
}

/**
 * Event data emitted when a step is clicked
 * @interface StepClickEvent
 */
export interface StepClickEvent {
  /** The step that was clicked */
  step: StepperItem;
  /** Index of the clicked step */
  stepIndex: number;
  /** Type of click action */
  action: 'toggle' | 'retry';
}

/**
 * Event data emitted when step status changes
 * @interface StepStatusChangeEvent
 */
export interface StepStatusChangeEvent {
  /** The step that changed */
  step: StepperItem;
  /** Index of the step */
  stepIndex: number;
  /** Previous status */
  previousStatus: StepStatus;
  /** New status */
  newStatus: StepStatus;
}

/**
 * Default configuration values for the stepper component
 */
export const DEFAULT_STEPPER_CONFIG: Required<StepperConfig> = {
  animationSpeed: 'normal',
  showTimers: true,
  showRetryButtons: true,
  enableCollapse: true,
  autoCollapseCompleted: true,
  enableTypewriter: true,
  customClasses: [],
  showStepNumbers: true,
  showConnectingLines: true,
};

/**
 * Animation speed to milliseconds mapping
 */
export const ANIMATION_SPEED_MAP: Record<AnimationSpeed, number> = {
  slow: 50,
  normal: 3,
  fast: 1,
  instant: 0,
};

/**
 * Reusable Vertical Stepper Component
 *
 * A highly configurable stepper component that supports:
 * - Custom step data with flexible configuration
 * - Typewriter animations and smooth transitions
 * - Light and dark themes
 * - Step collapse/expand functionality
 * - Timer display and retry mechanisms
 * - Event callbacks for step interactions
 *
 * @example
 * ```html
 * <app-standalone-vertical-stepper
 *   [steps]="mySteps"
 *   [theme]="'light'"
 *   [config]="stepperConfig"
 *   (stepClick)="onStepClick($event)"
 *   (statusChange)="onStatusChange($event)">
 * </app-standalone-vertical-stepper>
 * ```
 */
@Component({
  selector: 'app-standalone-vertical-stepper',
  standalone: true,
  imports: [CommonModule, MarkdownModule],
  templateUrl: './standalone-vertical-stepper.component.html',
  styleUrls: ['./standalone-vertical-stepper.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class StandaloneVerticalStepperComponent implements OnInit, OnDestroy, OnChanges {

  // Input Properties

  /**
   * Array of step data to display in the stepper
   * @default []
   */
  @Input() steps: StepperItem[] = [];

  /**
   * Visual theme for the stepper component
   * @default 'light'
   */
  @Input() theme: StepperTheme = 'light';

  /**
   * Overall status of the stepper process
   * @default 'PENDING'
   */
  @Input() status: StepperStatus = 'PENDING';

  /**
   * Configuration options for the stepper behavior and appearance
   * @default DEFAULT_STEPPER_CONFIG
   */
  @Input() config: Partial<StepperConfig> = {};

  /**
   * Whether to show a restart button
   * @default false
   */
  @Input() restartable: boolean = false;

  /**
   * Index of the currently active step
   * @default 0
   */
  @Input() currentStepIndex: number = 0;

  /**
   * Demo mode for backward compatibility (optional)
   * When provided, uses predefined demo data instead of steps input
   */
  @Input() demoMode?: 'completed' | 'in-progress' | 'failed';

  // Output Events

  /**
   * Emitted when a step is clicked (toggle or retry)
   */
  @Output() stepClick = new EventEmitter<StepClickEvent>();

  /**
   * Emitted when a step's status changes
   */
  @Output() statusChange = new EventEmitter<StepStatusChangeEvent>();

  /**
   * Emitted when the restart button is clicked
   */
  @Output() restart = new EventEmitter<void>();

  /**
   * Emitted when a step's timer updates
   */
  @Output() timerUpdate = new EventEmitter<{ stepIndex: number; elapsedTime: number }>();

  // Internal Properties

  /** Internal step data with runtime properties */
  internalSteps: InternalStepperItem[] = [];

  /** Currently active step */
  currentStep: InternalStepperItem | null = null;

  /** Whether line animation is currently running */
  animatingLine: boolean = false;

  /** Merged configuration with defaults */
  mergedConfig: Required<StepperConfig> = DEFAULT_STEPPER_CONFIG;

  // Private Properties

  /** Timeout references for cleanup */
  private timeoutRefs: { [key: string]: any } = {};

  /** Current typing speed in milliseconds */
  private typingSpeed: number = 3;

  /** Index of user-expanded step (excluding processing step) */
  private userExpandedStep: number | null = null;

  /** Timer interval reference */
  private timerInterval: any;

  /** Timer update interval in milliseconds */
  private readonly timerUpdateInterval: number = 1000;

  /** Set of collapsed step indices */
  private collapsedSteps: Set<number> = new Set();

  /** Set of expanding step indices */
  private expandingSteps: Set<number> = new Set();

  /** Set of collapsing step indices */
  private collapsingSteps: Set<number> = new Set();

  /** Animation duration constant */
  private readonly ANIMATION_DURATION_MS = 500;

  constructor(private cdr: ChangeDetectorRef) {}

  /**
   * Component initialization
   */
  ngOnInit(): void {
    this.initializeComponent();
  }

  /**
   * Handle input changes
   */
  ngOnChanges(changes: SimpleChanges): void {
    if (changes['steps'] || changes['config'] || changes['demoMode']) {
      this.initializeComponent();
    }

    if (changes['currentStepIndex']) {
      this.updateCurrentStep();
    }

    if (changes['status']) {
      this.updateStepperStatus();
    }
  }

  /**
   * Component cleanup
   */
  ngOnDestroy(): void {
    this.clearAllTimeouts();
    this.stopTimer();
  }

  // Initialization Methods

  /**
   * Initialize the component with configuration and data
   * @private
   */
  private initializeComponent(): void {
    this.mergeConfiguration();
    this.updateTypingSpeed();

    if (this.demoMode) {
      this.initializeDemoData();
    } else {
      this.initializeStepsFromInput();
    }

    this.updateCurrentStep();
    this.startInitialAnimations();
  }

  /**
   * Merge user configuration with defaults
   * @private
   */
  private mergeConfiguration(): void {
    this.mergedConfig = { ...DEFAULT_STEPPER_CONFIG, ...this.config };
  }

  /**
   * Update typing speed based on configuration
   * @private
   */
  private updateTypingSpeed(): void {
    this.typingSpeed = ANIMATION_SPEED_MAP[this.mergedConfig.animationSpeed];
  }

  /**
   * Initialize steps from input data
   * @private
   */
  private initializeStepsFromInput(): void {
    this.internalSteps = this.steps.map((step, index) => this.createInternalStep(step, index));
    this.validateSteps();
    this.applyInitialStepStates();
  }

  /**
   * Create internal step from input step data
   * @private
   */
  private createInternalStep(step: StepperItem, index: number): InternalStepperItem {
    return {
      ...step,
      id: step.id || `step-${index}`,
      visibleTitle: step.title,
      visibleDescription: step.description,
      active: step.status === 'active',
      completed: step.status === 'completed',
      collapsed: step.collapsed ?? (this.mergedConfig.autoCollapseCompleted && step.status === 'completed'),
      collapsible: step.collapsible ?? this.mergedConfig.enableCollapse,
      timerActive: step.timerActive ?? (step.status === 'active' && this.mergedConfig.showTimers),
      maxRetries: step.maxRetries ?? 3,
      retryCount: step.retryCount ?? 0,
    };
  }

  /**
   * Validate step data and log warnings for issues
   * @private
   */
  private validateSteps(): void {
    if (this.internalSteps.length === 0) {
      console.warn('StandaloneVerticalStepperComponent: No steps provided');
      return;
    }

    const activeSteps = this.internalSteps.filter(step => step.active);
    if (activeSteps.length > 1) {
      console.warn('StandaloneVerticalStepperComponent: Multiple active steps detected. Only one step should be active.');
    }

    this.internalSteps.forEach((step, index) => {
      if (!step.title?.trim()) {
        console.warn(`StandaloneVerticalStepperComponent: Step ${index} has empty title`);
      }
    });
  }

  /**
   * Apply initial states to steps based on configuration
   * @private
   */
  private applyInitialStepStates(): void {
    this.internalSteps.forEach((step, index) => {
      if (step.collapsed) {
        this.collapsedSteps.add(index);
      }

      // Reset visible text for typewriter effect
      if (this.mergedConfig.enableTypewriter) {
        step.visibleTitle = '';
        step.visibleDescription = '';
      }
    });
  }

  /**
   * Initialize demo data for backward compatibility
   * @private
   */
  private initializeDemoData(): void {
    switch (this.demoMode) {
      case 'completed':
        this.initializeCompletedDemo();
        break;
      case 'in-progress':
        this.initializeInProgressDemo();
        break;
      case 'failed':
        this.initializeFailedDemo();
        break;
    }
  }

  /**
   * Initialize completed demo scenario
   * @private
   */
  private initializeCompletedDemo(): void {
    this.status = 'COMPLETED';
    const demoSteps: StepperItem[] = [
      {
        id: 'demo-overview',
        title: 'Project Overview',
        description: '**Project Analysis Complete**\n\nSuccessfully analyzed the uploaded design and identified key components:\n- Navigation header with logo and menu items\n- Hero section with call-to-action\n- Feature cards layout\n- Footer with contact information\n\nReady to proceed with component generation.',
        status: 'completed',
        completionTime: 15,
        collapsed: true
      },
      {
        id: 'demo-components',
        title: 'Components Identified',
        description: '**Component Structure Mapped**\n\nIdentified and catalogued all UI components:\n\n- **Header Component**: Navigation with responsive menu\n- **Hero Component**: Main banner with CTA button\n- **Card Component**: Reusable feature cards\n- **Footer Component**: Contact and social links\n\nComponent hierarchy and dependencies established.',
        status: 'completed',
        completionTime: 23,
        collapsed: true
      }
    ];

    this.internalSteps = demoSteps.map((step, index) => this.createInternalStep(step, index));
    this.applyInitialStepStates();
  }

  /**
   * Initialize in-progress demo scenario
   * @private
   */
  private initializeInProgressDemo(): void {
    this.status = 'IN_PROGRESS';
    const demoSteps: StepperItem[] = [
      {
        id: 'demo-overview-progress',
        title: 'Project Overview',
        description: '**Project Analysis Complete**\n\nSuccessfully analyzed the uploaded design and identified key components.',
        status: 'completed',
        completionTime: 15,
        collapsed: true
      },
      {
        id: 'demo-building',
        title: 'Building Application',
        description: '**Build Process Started**\n\nCurrently compiling and building your application:\n\n- Installing dependencies...\n- Compiling TypeScript files...\n- Processing styles and assets...\n- Running build optimizations...\n\nThis may take a few moments.',
        status: 'active',
        timerActive: true,
        startTime: Date.now(),
        collapsed: false
      }
    ];

    this.internalSteps = demoSteps.map((step, index) => this.createInternalStep(step, index));
    this.currentStepIndex = 1;
    this.applyInitialStepStates();
  }

  /**
   * Initialize failed demo scenario
   * @private
   */
  private initializeFailedDemo(): void {
    this.status = 'FAILED';
    const demoSteps: StepperItem[] = [
      {
        id: 'demo-overview-failed',
        title: 'Project Overview',
        description: '**Project Analysis Complete**\n\nSuccessfully analyzed the uploaded design.',
        status: 'completed',
        completionTime: 15,
        collapsed: true
      },
      {
        id: 'demo-build-failed',
        title: 'Build Failed',
        description: '**Build Process Failed**\n\n❌ **Error Details:**\n- TypeScript compilation errors detected\n- Missing dependency: @types/react\n- Syntax error in component file\n\n**Suggested Actions:**\n- Check component syntax\n- Install missing dependencies\n- Review TypeScript configuration\n\nClick retry to attempt build again.',
        status: 'failed',
        retryCount: 1,
        collapsed: false
      }
    ];

    this.internalSteps = demoSteps.map((step, index) => this.createInternalStep(step, index));
    this.currentStepIndex = 1;
    this.applyInitialStepStates();
  }

  /**
   * Update current step based on currentStepIndex
   * @private
   */
  private updateCurrentStep(): void {
    if (this.currentStepIndex >= 0 && this.currentStepIndex < this.internalSteps.length) {
      this.currentStep = this.internalSteps[this.currentStepIndex];
    } else {
      this.currentStep = null;
    }
  }

  /**
   * Update stepper status and apply related changes
   * @private
   */
  private updateStepperStatus(): void {
    // Update step states based on overall status
    this.internalSteps.forEach((step, index) => {
      if (this.status === 'IN_PROGRESS' && index === this.currentStepIndex) {
        step.active = true;
        step.timerActive = this.mergedConfig.showTimers;
      } else {
        step.active = false;
        step.timerActive = false;
      }
    });
  }

  /**
   * Start initial animations for the stepper
   * @private
   */
  private startInitialAnimations(): void {
    if (this.internalSteps.length > 0) {
      this.updateCurrentStep();

      if (this.status === 'IN_PROGRESS') {
        this.collapsedSteps.delete(this.currentStepIndex);
      }

      if (this.mergedConfig.enableTypewriter) {
        setTimeout(() => {
          this.startTypewriterAnimation(this.currentStepIndex);
        }, 100);
      }

      this.startTimer();
    }
  }

  // Animation and Interaction Methods

  /**
   * Start typewriter animation for a specific step
   * @param stepIndex - Index of the step to animate
   */
  startTypewriterAnimation(stepIndex: number): void {
    if (!this.mergedConfig.enableTypewriter) return;
    if (stepIndex < 0 || stepIndex >= this.internalSteps.length) return;

    const step = this.internalSteps[stepIndex];
    if (!step || step.isTyping || step.isTitleTyping) return;

    // Start title typing
    step.isTitleTyping = true;
    step.visibleTitle = '';
    this.typeText(step, 'title', stepIndex);
  }

  /**
   * Type text character by character for typewriter effect
   * @private
   */
  private typeText(step: InternalStepperItem, type: 'title' | 'description', stepIndex: number): void {
    const sourceText = type === 'title' ? step.title : step.description;
    const currentText = type === 'title' ? step.visibleTitle : step.visibleDescription;

    if (this.typingSpeed === 0) {
      // Instant mode - show all text immediately
      if (type === 'title') {
        step.visibleTitle = sourceText;
        step.isTitleTyping = false;
        // Start description typing
        setTimeout(() => {
          step.isTyping = true;
          this.typeText(step, 'description', stepIndex);
        }, 50);
      } else {
        step.visibleDescription = sourceText;
        step.isTyping = false;
      }
      this.cdr.markForCheck();
      return;
    }

    if (currentText.length < sourceText.length) {
      const nextChar = sourceText.charAt(currentText.length);

      if (type === 'title') {
        step.visibleTitle += nextChar;
      } else {
        step.visibleDescription += nextChar;
      }

      this.cdr.markForCheck();

      this.timeoutRefs[`typing-${type}-${stepIndex}`] = setTimeout(() => {
        this.typeText(step, type, stepIndex);
      }, this.typingSpeed);
    } else {
      // Typing complete
      if (type === 'title') {
        step.isTitleTyping = false;
        // Start description typing after title is complete
        setTimeout(() => {
          step.isTyping = true;
          step.visibleDescription = '';
          this.typeText(step, 'description', stepIndex);
        }, 200);
      } else {
        step.isTyping = false;
      }
      this.cdr.markForCheck();
    }
  }

  // Step Status and Interaction Methods

  /**
   * Get the display status of a step
   * @param index - Step index
   * @returns Status string for CSS classes
   */
  getStepStatus(index: number): string {
    if (index >= this.internalSteps.length) return '';

    const step = this.internalSteps[index];
    if (step.completed) return 'completed';
    if (step.active) return 'active';
    if (step.status === 'failed') return 'failed';
    return 'pending';
  }

  /**
   * Check if a step should be displayed
   * @param index - Step index
   * @returns Whether the step should be shown
   */
  shouldShowStep(index: number): boolean {
    return index < this.internalSteps.length;
  }

  isStepCollapsed(index: number): boolean {
    return this.collapsedSteps.has(index);
  }

  isStepExpanding(index: number): boolean {
    return this.expandingSteps.has(index);
  }

  isStepExpanded(index: number): boolean {
    return !this.collapsedSteps.has(index) && !this.expandingSteps.has(index) && !this.collapsingSteps.has(index);
  }

  isStepCollapsing(index: number): boolean {
    return this.collapsingSteps.has(index);
  }

  shouldHideStepLine(index: number): boolean {
    return index >= this.steps.length - 1;
  }

  isLineAnimating(index: number): boolean {
    return this.animatingLine && index === this.currentStepIndex - 1;
  }

  isProcessingStep(index: number): boolean {
    return this.status === 'IN_PROGRESS' && index === this.currentStepIndex;
  }

  /**
   * Check if a step can be collapsed
   * @param index - Step index
   * @returns Whether the step can be collapsed
   */
  canStepBeCollapsed(index: number): boolean {
    if (!this.mergedConfig.enableCollapse) return false;
    const step = this.internalSteps[index];
    return step?.collapsible !== false && (step?.completed || this.isFailureStep(step));
  }

  /**
   * Check if a step is in a failure state
   * @param step - Step to check
   * @returns Whether the step is failed
   */
  isFailureStep(step: InternalStepperItem): boolean {
    return step?.status === 'failed' || step?.title?.toLowerCase().includes('failed') || (step?.retryCount ?? 0) > 0;
  }

  /**
   * Toggle step collapse/expand state
   * @param index - Step index
   */
  toggleStepCollapse(index: number): void {
    if (!this.canStepBeCollapsed(index)) return;

    const step = this.internalSteps[index];
    const isCurrentlyCollapsed = this.collapsedSteps.has(index);

    // Emit step click event
    this.stepClick.emit({
      step: this.steps[index] || step,
      stepIndex: index,
      action: 'toggle'
    });

    if (isCurrentlyCollapsed) {
      // Expand step
      this.expandingSteps.add(index);
      this.collapsedSteps.delete(index);

      // Collapse other user-expanded steps (except processing step)
      if (this.userExpandedStep !== null && this.userExpandedStep !== index) {
        this.collapsedSteps.add(this.userExpandedStep);
      }
      this.userExpandedStep = index;

      setTimeout(() => {
        this.expandingSteps.delete(index);
        this.cdr.markForCheck();
      }, this.ANIMATION_DURATION_MS);
    } else {
      // Collapse step
      this.collapsingSteps.add(index);

      setTimeout(() => {
        this.collapsedSteps.add(index);
        this.collapsingSteps.delete(index);
        if (this.userExpandedStep === index) {
          this.userExpandedStep = null;
        }
        this.cdr.markForCheck();
      }, this.ANIMATION_DURATION_MS);
    }

    this.cdr.markForCheck();
  }

  // Timer Functionality

  /**
   * Start the timer for active steps
   */
  startTimer(): void {
    if (!this.mergedConfig.showTimers) return;

    this.stopTimer();

    this.timerInterval = setInterval(() => {
      this.updateTimers();
    }, this.timerUpdateInterval);
  }

  /**
   * Stop the timer
   */
  stopTimer(): void {
    if (this.timerInterval) {
      clearInterval(this.timerInterval);
      this.timerInterval = null;
    }
  }

  /**
   * Update timers for all active steps
   * @private
   */
  private updateTimers(): void {
    let hasActiveTimer = false;

    this.internalSteps.forEach((step, index) => {
      if (step.timerActive && step.startTime) {
        const newElapsedTime = Math.floor((Date.now() - step.startTime) / 1000);
        if (step.elapsedTime !== newElapsedTime) {
          step.elapsedTime = newElapsedTime;
          this.timerUpdate.emit({ stepIndex: index, elapsedTime: newElapsedTime });
        }
        hasActiveTimer = true;
      }
    });

    if (hasActiveTimer) {
      this.cdr.markForCheck();
    }
  }

  /**
   * Format elapsed time for display
   * @param seconds - Time in seconds
   * @returns Formatted time string (mm:ss)
   */
  formatElapsedTime(seconds: number): string {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  }

  // Utility Methods

  /**
   * Format step title for display
   * @param title - Raw title string
   * @returns Formatted title
   */
  formatTitle(title: string): string {
    return title;
  }

  /**
   * Get sanitized description for display
   * @param description - Raw description string
   * @returns Sanitized description
   */
  getSanitizedDescription(description: string): string {
    return description;
  }

  /**
   * Check if retry button should be shown for a step
   * @param step - Step to check
   * @param index - Step index
   * @returns Whether to show retry button
   */
  shouldShowRetryButton(step: InternalStepperItem, index: number): boolean {
    return this.mergedConfig.showRetryButtons &&
           this.isFailureStep(step) &&
           (step.retryCount ?? 0) < (step.maxRetries ?? 3);
  }

  /**
   * Handle retry button click
   * @param index - Step index
   * @param event - Click event
   */
  onRetryClick(index: number, event?: Event): void {
    if (event) {
      event.stopPropagation();
    }

    const step = this.internalSteps[index];
    const originalStep = this.steps[index];

    if (step && (step.retryCount ?? 0) < (step.maxRetries ?? 3)) {
      step.retryCount = (step.retryCount ?? 0) + 1;
      step.isRetrying = true;

      // Emit step click event
      this.stepClick.emit({
        step: originalStep || step,
        stepIndex: index,
        action: 'retry'
      });

      // Simulate retry process (in real implementation, this would trigger actual retry logic)
      setTimeout(() => {
        step.isRetrying = false;
        // Update description for demo purposes
        step.description = `**Retry Attempt ${step.retryCount}**\n\nRetrying process...\n\nPlease wait while we attempt to resolve the previous issues.`;
        step.visibleDescription = step.description;
        this.cdr.markForCheck();
      }, 2000);
    }
  }

  /**
   * Restart the stepper process
   */
  restartStepper(): void {
    this.clearAllTimeouts();
    this.stopTimer();
    this.restart.emit();

    // Reset internal state
    this.collapsedSteps.clear();
    this.expandingSteps.clear();
    this.collapsingSteps.clear();
    this.userExpandedStep = null;

    this.initializeComponent();
  }

  /**
   * Clear all timeout references
   * @private
   */
  private clearAllTimeouts(): void {
    Object.values(this.timeoutRefs).forEach(timeoutId => clearTimeout(timeoutId));
    this.timeoutRefs = {};
  }

  // Public API Methods for External Control

  /**
   * Programmatically update a step's status
   * @param stepIndex - Index of the step to update
   * @param newStatus - New status for the step
   */
  updateStepStatus(stepIndex: number, newStatus: StepStatus): void {
    if (stepIndex < 0 || stepIndex >= this.internalSteps.length) return;

    const step = this.internalSteps[stepIndex];
    const originalStep = this.steps[stepIndex];
    const previousStatus = step.status;

    step.status = newStatus;
    step.active = newStatus === 'active';
    step.completed = newStatus === 'completed';

    // Update original step if it exists
    if (originalStep) {
      originalStep.status = newStatus;
    }

    // Emit status change event
    this.statusChange.emit({
      step: originalStep || step,
      stepIndex,
      previousStatus,
      newStatus
    });

    this.cdr.markForCheck();
  }

  /**
   * Programmatically set the current active step
   * @param stepIndex - Index of the step to make active
   */
  setActiveStep(stepIndex: number): void {
    if (stepIndex < 0 || stepIndex >= this.internalSteps.length) return;

    // Deactivate all steps
    this.internalSteps.forEach(step => {
      step.active = false;
      step.timerActive = false;
    });

    // Activate the specified step
    const step = this.internalSteps[stepIndex];
    step.active = true;
    step.status = 'active';
    step.timerActive = this.mergedConfig.showTimers;
    step.startTime = Date.now();

    this.currentStepIndex = stepIndex;
    this.currentStep = step;

    this.cdr.markForCheck();
  }

  /**
   * Get current component configuration
   * @returns Current merged configuration
   */
  getConfig(): Required<StepperConfig> {
    return { ...this.mergedConfig };
  }

  /**
   * Get current step data
   * @returns Array of current internal steps
   */
  getCurrentSteps(): InternalStepperItem[] {
    return [...this.internalSteps];
  }
}
