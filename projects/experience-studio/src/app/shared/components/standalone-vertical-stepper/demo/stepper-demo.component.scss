.stepper-demo-container {
  min-height: 100vh;
  padding: 2rem;
  background-color: #f8f9fa;
  transition: all 0.3s ease;

  &.dark-theme {
    background-color: #1a1a1a;
    color: #ffffff;
  }
}

.demo-header {
  text-align: center;
  margin-bottom: 2rem;

  h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: #2d3748;

    .dark-theme & {
      color: #ffffff;
    }
  }

  p {
    font-size: 1.1rem;
    color: #4a5568;
    margin: 0;

    .dark-theme & {
      color: #cbd5e0;
    }
  }
}

.demo-controls {
  display: flex;
  flex-wrap: wrap;
  gap: 2rem;
  justify-content: center;
  margin-bottom: 3rem;
  padding: 1.5rem;
  background-color: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);

  .dark-theme & {
    background-color: #2d3748;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
  }

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 1rem;
  }
}

.control-group {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap;

  label {
    font-weight: 600;
    margin-right: 0.5rem;
    color: #2d3748;
    white-space: nowrap;
    min-width: fit-content;

    .dark-theme & {
      color: #e2e8f0;
    }
  }

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;

    label {
      margin-right: 0;
      margin-bottom: 0.25rem;
    }
  }
}

.control-button {
  padding: 0.5rem 1rem;
  border: 2px solid #e2e8f0;
  background-color: #ffffff;
  color: #4a5568;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 500;
  white-space: nowrap;

  &:hover {
    border-color: #6b46c1;
    color: #6b46c1;
  }

  &.active {
    background-color: #6b46c1;
    border-color: #6b46c1;
    color: #ffffff;
  }

  .dark-theme & {
    background-color: #4a5568;
    border-color: #4a5568;
    color: #e2e8f0;

    &:hover {
      border-color: #9f7aea;
      color: #9f7aea;
    }

    &.active {
      background-color: #9f7aea;
      border-color: #9f7aea;
      color: #1a1a1a;
    }
  }

  &.action-button {
    background-color: #10b981;
    border-color: #10b981;
    color: white;

    &:hover {
      background-color: #059669;
      border-color: #059669;
    }

    &.failure {
      background-color: #ef4444;
      border-color: #ef4444;

      &:hover {
        background-color: #dc2626;
        border-color: #dc2626;
      }
    }

    .dark-theme & {
      background-color: #10b981;
      border-color: #10b981;

      &:hover {
        background-color: #059669;
        border-color: #059669;
      }

      &.failure {
        background-color: #ef4444;
        border-color: #ef4444;

        &:hover {
          background-color: #dc2626;
          border-color: #dc2626;
        }
      }
    }
  }
}

.demo-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  max-width: 1400px;
  margin: 0 auto;

  @media (max-width: 1024px) {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
}

.stepper-wrapper {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  min-height: 600px;

  .dark-theme & {
    background-color: #2d3748;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
  }
}

.demo-info {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);

  .dark-theme & {
    background-color: #2d3748;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
  }

  h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #2d3748;
    border-bottom: 2px solid #e2e8f0;
    padding-bottom: 0.5rem;

    .dark-theme & {
      color: #e2e8f0;
      border-bottom-color: #4a5568;
    }
  }

  ul {
    list-style: none;
    padding: 0;
    margin-bottom: 2rem;

    li {
      padding: 0.5rem 0;
      color: #4a5568;
      font-size: 0.95rem;

      .dark-theme & {
        color: #cbd5e0;
      }
    }
  }

  pre {
    background-color: #f7fafc;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    padding: 1rem;
    overflow-x: auto;
    font-size: 0.875rem;
    margin: 1rem 0;

    .dark-theme & {
      background-color: #1a202c;
      border-color: #4a5568;
    }

    code {
      color: #2d3748;
      font-family: 'Consolas', 'Monaco', 'Courier New', monospace;

      .dark-theme & {
        color: #e2e8f0;
      }
    }
  }
}

// CSS variables for the stepper component
:host {
  --chat-window-card-bg-color: #ffffff;
  --color-primary: #6b46c1;
  --color-primary-light: #9f7aea;
  --text-primary: #2d3748;
  --text-primary-dark: #f8f9fa;
  --text-secondary: #4a5568;
  --text-secondary-dark: #cbd5e0;
  --body-text-color: #2d3748;
  --markdown-inline-code-bg: rgba(0, 0, 0, 0.05);
  --markdown-inline-code-text: #2d3748;
  --code-viewer-text: #ffffff;

  .dark-theme & {
    --chat-window-card-bg-color: #2d3748;
    --body-text-color: #e2e8f0;
    --markdown-inline-code-bg: rgba(255, 255, 255, 0.1);
    --markdown-inline-code-text: #e2e8f0;
  }
}

// Action button styles
.action-button {
  &.failure {
    background-color: #e53e3e !important;
    border-color: #e53e3e !important;

    &:hover {
      background-color: #c53030 !important;
      border-color: #c53030 !important;
    }

    .dark-theme & {
      background-color: #fc8181 !important;
      border-color: #fc8181 !important;
      color: #1a1a1a !important;

      &:hover {
        background-color: #f56565 !important;
        border-color: #f56565 !important;
      }
    }
  }
}

// Demo stepper custom styling
.demo-stepper {
  border: 2px dashed rgba(156, 39, 176, 0.2);
  border-radius: 8px;
  padding: 1rem;

  .dark-theme & {
    border-color: rgba(159, 122, 234, 0.3);
  }
}
