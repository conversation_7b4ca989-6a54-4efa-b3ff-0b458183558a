<div class="stepper-demo-container" [class.dark-theme]="currentTheme === 'dark'">
  <div class="demo-header">
    <h1>Standalone Vertical Stepper Demo</h1>
    <p>A reusable vertical stepper component with hardcoded data for demonstration purposes.</p>
  </div>

  <div class="demo-controls">
    <div class="control-group">
      <label>Theme:</label>
      <button
        class="control-button"
        [class.active]="currentTheme === 'light'"
        (click)="currentTheme = 'light'">
        Light
      </button>
      <button
        class="control-button"
        [class.active]="currentTheme === 'dark'"
        (click)="currentTheme = 'dark'">
        Dark
      </button>
    </div>

    <div class="control-group">
      <label>Demo Mode:</label>
      <button
        class="control-button"
        [class.active]="currentMode === 'completed'"
        (click)="setMode('completed')">
        Completed
      </button>
      <button
        class="control-button"
        [class.active]="currentMode === 'in-progress'"
        (click)="setMode('in-progress')">
        In Progress
      </button>
      <button
        class="control-button"
        [class.active]="currentMode === 'failed'"
        (click)="setMode('failed')">
        Failed
      </button>
      <button
        class="control-button"
        [class.active]="currentMode === 'custom'"
        (click)="setMode('custom')">
        Custom Data
      </button>
    </div>

    <div class="control-group">
      <label>Animation Speed:</label>
      <button
        class="control-button"
        [class.active]="animationSpeed === 'slow'"
        (click)="animationSpeed = 'slow'; onConfigChange()">
        Slow
      </button>
      <button
        class="control-button"
        [class.active]="animationSpeed === 'normal'"
        (click)="animationSpeed = 'normal'; onConfigChange()">
        Normal
      </button>
      <button
        class="control-button"
        [class.active]="animationSpeed === 'fast'"
        (click)="animationSpeed = 'fast'; onConfigChange()">
        Fast
      </button>
      <button
        class="control-button"
        [class.active]="animationSpeed === 'instant'"
        (click)="animationSpeed = 'instant'; onConfigChange()">
        Instant
      </button>
    </div>

    <div class="control-group">
      <label>Features:</label>
      <button
        class="control-button"
        [class.active]="showTimers"
        (click)="showTimers = !showTimers; onConfigChange()">
        {{ showTimers ? 'Hide' : 'Show' }} Timers
      </button>
      <button
        class="control-button"
        [class.active]="enableTypewriter"
        (click)="enableTypewriter = !enableTypewriter; onConfigChange()">
        {{ enableTypewriter ? 'Disable' : 'Enable' }} Typewriter
      </button>
      <button
        class="control-button"
        [class.active]="enableCollapse"
        (click)="enableCollapse = !enableCollapse; onConfigChange()">
        {{ enableCollapse ? 'Disable' : 'Enable' }} Collapse
      </button>
      <button
        class="control-button"
        [class.active]="showRestartButton"
        (click)="toggleRestartButton()">
        {{ showRestartButton ? 'Hide' : 'Show' }} Restart
      </button>
    </div>

    @if (currentMode === 'custom') {
      <div class="control-group">
        <label>Actions:</label>
        <button
          class="control-button action-button"
          (click)="simulateStepCompletion()">
          Complete Current Step
        </button>
        <button
          class="control-button action-button failure"
          (click)="simulateStepFailure()">
          Fail Current Step
        </button>
      </div>
    }
  </div>

  <div class="demo-content">
    <div class="stepper-wrapper">
      @if (currentMode === 'custom') {
        <!-- Reusable component with custom data -->
        <app-standalone-vertical-stepper
          [steps]="customSteps"
          [theme]="currentTheme"
          [status]="customStatus"
          [config]="customConfig"
          [currentStepIndex]="currentStepIndex"
          [restartable]="showRestartButton"
          (stepClick)="onStepClick($event)"
          (statusChange)="onStatusChange($event)"
          (restart)="onRestart()"
          (timerUpdate)="onTimerUpdate($event)">
        </app-standalone-vertical-stepper>
      } @else {
        <!-- Demo mode for backward compatibility -->
        <app-standalone-vertical-stepper
          [theme]="currentTheme"
          [demoMode]="currentMode"
          [restartable]="showRestartButton"
          [config]="customConfig"
          (stepClick)="onStepClick($event)"
          (statusChange)="onStatusChange($event)"
          (restart)="onRestart()">
        </app-standalone-vertical-stepper>
      }
    </div>

    <div class="demo-info">
      <h3>Features Demonstrated:</h3>
      <ul>
        <li>✅ Typewriter animation effects</li>
        <li>✅ Step collapse/expand functionality</li>
        <li>✅ Light and dark theme support</li>
        <li>✅ Timer display for active steps</li>
        <li>✅ Retry functionality for failed steps</li>
        <li>✅ Smooth animations and transitions</li>
        <li>✅ Responsive design</li>
        <li>✅ Markdown content rendering</li>
        <li>✅ Loading spinners for active steps</li>
        <li>✅ Visual status indicators (checkmarks, X icons)</li>
      </ul>

      <h3>Basic Usage:</h3>
      <pre><code>&lt;!-- Demo Mode (Backward Compatibility) --&gt;
&lt;app-standalone-vertical-stepper
  [theme]="'light' | 'dark'"
  [demoMode]="'completed' | 'in-progress' | 'failed'"
  [restartable]="boolean"&gt;
&lt;/app-standalone-vertical-stepper&gt;

&lt;!-- Reusable Mode (Production Ready) --&gt;
&lt;app-standalone-vertical-stepper
  [steps]="stepperData"
  [theme]="'light' | 'dark'"
  [status]="'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'FAILED'"
  [config]="stepperConfig"
  [currentStepIndex]="activeStepIndex"
  [restartable]="boolean"
  (stepClick)="onStepClick($event)"
  (statusChange)="onStatusChange($event)"
  (restart)="onRestart()"
  (timerUpdate)="onTimerUpdate($event)"&gt;
&lt;/app-standalone-vertical-stepper&gt;</code></pre>

      <h3>TypeScript Interfaces:</h3>
<!--&lt;!&ndash;      <pre><code>interface StepperItem {{&ndash;&gt;-->
<!--&lt;!&ndash;  id?: string;&ndash;&gt;-->
<!--&lt;!&ndash;  title: string;&ndash;&gt;-->
<!--&lt;!&ndash;  description: string;&ndash;&gt;-->
<!--&lt;!&ndash;  status: 'pending' | 'active' | 'completed' | 'failed';&ndash;&gt;-->
<!--&lt;!&ndash;  collapsible?: boolean;&ndash;&gt;-->
<!--&lt;!&ndash;  collapsed?: boolean;&ndash;&gt;-->
<!--&lt;!&ndash;  startTime?: number;&ndash;&gt;-->
<!--&lt;!&ndash;  elapsedTime?: number;&ndash;&gt;-->
<!--&lt;!&ndash;  completionTime?: number;&ndash;&gt;-->
<!--&lt;!&ndash;  timerActive?: boolean;&ndash;&gt;-->
<!--&lt;!&ndash;  retryCount?: number;&ndash;&gt;-->
<!--&lt;!&ndash;  maxRetries?: number;&ndash;&gt;-->
<!--&lt;!&ndash;  data?: any;&ndash;&gt;-->
<!--&lt;!&ndash;}}&ndash;&gt;-->

<!--interface StepperConfig {{-->
<!--  animationSpeed?: 'slow' | 'normal' | 'fast' | 'instant';-->
<!--  showTimers?: boolean;-->
<!--  showRetryButtons?: boolean;-->
<!--  enableCollapse?: boolean;-->
<!--  autoCollapseCompleted?: boolean;-->
<!--  enableTypewriter?: boolean;-->
<!--  customClasses?: string[];-->
<!--  showStepNumbers?: boolean;-->
<!--  showConnectingLines?: boolean;-->
<!--}}</code></pre>-->

      <h3>Input Properties:</h3>
      <ul>
        <li><strong>steps</strong>: Array of StepperItem objects (for reusable mode)</li>
        <li><strong>theme</strong>: 'light' | 'dark' visual theme</li>
        <li><strong>status</strong>: Overall stepper status</li>
        <li><strong>config</strong>: Configuration object for behavior and appearance</li>
        <li><strong>currentStepIndex</strong>: Index of currently active step</li>
        <li><strong>restartable</strong>: Whether to show restart button</li>
        <li><strong>demoMode</strong>: Demo mode for backward compatibility</li>
      </ul>

      <h3>Output Events:</h3>
      <ul>
        <li><strong>stepClick</strong>: Emitted when a step is clicked (toggle/retry)</li>
        <li><strong>statusChange</strong>: Emitted when step status changes</li>
        <li><strong>restart</strong>: Emitted when restart button is clicked</li>
        <li><strong>timerUpdate</strong>: Emitted when step timer updates</li>
      </ul>

      <h3>Key Improvements:</h3>
      <ul>
        <li>✅ Fully configurable with TypeScript interfaces</li>
        <li>✅ Event-driven architecture with proper callbacks</li>
        <li>✅ Backward compatible with demo modes</li>
        <li>✅ Production-ready with custom data support</li>
        <li>✅ Comprehensive configuration options</li>
        <li>✅ Type-safe implementation with proper validation</li>
        <li>✅ Angular best practices (OnChanges, proper inputs/outputs)</li>
        <li>✅ Flexible animation and display controls</li>
      </ul>
    </div>
  </div>
</div>
