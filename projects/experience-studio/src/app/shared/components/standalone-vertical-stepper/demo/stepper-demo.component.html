<div class="stepper-demo-container" [class.dark-theme]="currentTheme === 'dark'">
  <div class="demo-header">
    <h1>Standalone Vertical Stepper Demo</h1>
    <p>A reusable vertical stepper component with hardcoded data for demonstration purposes.</p>
  </div>

  <div class="demo-controls">
    <div class="control-group">
      <label>Theme:</label>
      <button 
        class="control-button" 
        [class.active]="currentTheme === 'light'"
        (click)="currentTheme = 'light'">
        Light
      </button>
      <button 
        class="control-button" 
        [class.active]="currentTheme === 'dark'"
        (click)="currentTheme = 'dark'">
        Dark
      </button>
    </div>

    <div class="control-group">
      <label>Demo Mode:</label>
      <button 
        class="control-button" 
        [class.active]="currentMode === 'completed'"
        (click)="setMode('completed')">
        Completed
      </button>
      <button 
        class="control-button" 
        [class.active]="currentMode === 'in-progress'"
        (click)="setMode('in-progress')">
        In Progress
      </button>
      <button 
        class="control-button" 
        [class.active]="currentMode === 'failed'"
        (click)="setMode('failed')">
        Failed
      </button>
    </div>

    <div class="control-group">
      <label>Options:</label>
      <button 
        class="control-button" 
        [class.active]="showRestartButton"
        (click)="toggleRestartButton()">
        {{ showRestartButton ? 'Hide' : 'Show' }} Restart Button
      </button>
    </div>
  </div>

  <div class="demo-content">
    <div class="stepper-wrapper">
      <app-standalone-vertical-stepper
        [theme]="currentTheme"
        [demoMode]="currentMode"
        [restartable]="showRestartButton">
      </app-standalone-vertical-stepper>
    </div>

    <div class="demo-info">
      <h3>Features Demonstrated:</h3>
      <ul>
        <li>✅ Typewriter animation effects</li>
        <li>✅ Step collapse/expand functionality</li>
        <li>✅ Light and dark theme support</li>
        <li>✅ Timer display for active steps</li>
        <li>✅ Retry functionality for failed steps</li>
        <li>✅ Smooth animations and transitions</li>
        <li>✅ Responsive design</li>
        <li>✅ Markdown content rendering</li>
        <li>✅ Loading spinners for active steps</li>
        <li>✅ Visual status indicators (checkmarks, X icons)</li>
      </ul>

      <h3>Usage:</h3>
      <pre><code>&lt;app-standalone-vertical-stepper
  [theme]="'light' | 'dark'"
  [demoMode]="'completed' | 'in-progress' | 'failed'"
  [restartable]="boolean"&gt;
&lt;/app-standalone-vertical-stepper&gt;</code></pre>

      <h3>Key Differences from Original:</h3>
      <ul>
        <li>🔄 Removed all API dependencies and polling services</li>
        <li>🔄 Replaced dynamic data with hardcoded demo data</li>
        <li>🔄 Simplified component interface with demo-specific inputs</li>
        <li>🔄 Maintained all visual styling and animations</li>
        <li>🔄 Preserved core stepper functionality</li>
        <li>🔄 Added demo modes for different scenarios</li>
      </ul>
    </div>
  </div>
</div>
