import { Component, ChangeDetectionStrategy } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  StandaloneVerticalStepperComponent,
  StepperItem,
  StepperConfig,
  StepperTheme,
  StepperStatus,
  StepClickEvent,
  StepStatusChangeEvent
} from '../standalone-vertical-stepper.component';

@Component({
  selector: 'app-stepper-demo',
  standalone: true,
  imports: [CommonModule, StandaloneVerticalStepperComponent],
  templateUrl: './stepper-demo.component.html',
  styleUrls: ['./stepper-demo.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class StepperDemoComponent {
  // Demo mode properties (for backward compatibility)
  currentTheme: StepperTheme = 'light';
  currentMode: 'completed' | 'in-progress' | 'failed' | 'custom' = 'completed';
  showRestartButton = false;

  // Reusable component properties
  customSteps: StepperItem[] = [];
  customStatus: StepperStatus = 'PENDING';
  customConfig: Partial<StepperConfig> = {};
  currentStepIndex = 0;

  // Demo configuration options
  animationSpeed: 'slow' | 'normal' | 'fast' | 'instant' = 'normal';
  showTimers = true;
  showRetryButtons = true;
  enableCollapse = true;
  autoCollapseCompleted = true;
  enableTypewriter = true;
  showStepNumbers = true;
  showConnectingLines = true;

  constructor() {
    this.initializeCustomSteps();
    this.updateConfig();
  }

  // Demo mode methods
  toggleTheme(): void {
    this.currentTheme = this.currentTheme === 'light' ? 'dark' : 'light';
  }

  setMode(mode: 'completed' | 'in-progress' | 'failed' | 'custom'): void {
    this.currentMode = mode;
    if (mode === 'custom') {
      this.initializeCustomSteps();
    }
  }

  toggleRestartButton(): void {
    this.showRestartButton = !this.showRestartButton;
  }

  // Configuration methods
  updateConfig(): void {
    this.customConfig = {
      animationSpeed: this.animationSpeed,
      showTimers: this.showTimers,
      showRetryButtons: this.showRetryButtons,
      enableCollapse: this.enableCollapse,
      autoCollapseCompleted: this.autoCollapseCompleted,
      enableTypewriter: this.enableTypewriter,
      showStepNumbers: this.showStepNumbers,
      showConnectingLines: this.showConnectingLines,
      customClasses: ['demo-stepper']
    };
  }

  onConfigChange(): void {
    this.updateConfig();
  }

  // Custom steps initialization
  initializeCustomSteps(): void {
    this.customSteps = [
      {
        id: 'custom-step-1',
        title: 'Initialize Project',
        description: '**Setting up the project structure**\n\nCreating the basic project scaffolding:\n- Setting up directory structure\n- Installing dependencies\n- Configuring build tools\n\nThis step establishes the foundation for your application.',
        status: 'completed',
        completionTime: 12,
        collapsible: true
      },
      {
        id: 'custom-step-2',
        title: 'Configure Environment',
        description: '**Environment Configuration**\n\nSetting up development and production environments:\n- Environment variables\n- Database connections\n- API endpoints\n- Security configurations\n\nEnsuring proper environment isolation.',
        status: 'completed',
        completionTime: 8,
        collapsible: true
      },
      {
        id: 'custom-step-3',
        title: 'Generate Components',
        description: '**Component Generation in Progress**\n\nCreating reusable UI components:\n- ✅ Header component\n- ✅ Navigation component\n- 🔄 Content components\n- ⏳ Footer component\n\nGenerating clean, maintainable code...',
        status: 'active',
        timerActive: true,
        startTime: Date.now(),
        collapsible: false
      },
      {
        id: 'custom-step-4',
        title: 'Run Tests',
        description: 'Execute comprehensive test suite to ensure code quality and functionality.',
        status: 'pending',
        collapsible: true
      },
      {
        id: 'custom-step-5',
        title: 'Deploy Application',
        description: 'Deploy the application to production environment with proper monitoring and logging.',
        status: 'pending',
        collapsible: true
      }
    ];

    this.customStatus = 'IN_PROGRESS';
    this.currentStepIndex = 2;
  }

  // Event handlers
  onStepClick(event: StepClickEvent): void {
    console.log('Step clicked:', event);
  }

  onStatusChange(event: StepStatusChangeEvent): void {
    console.log('Status changed:', event);
  }

  onRestart(): void {
    console.log('Stepper restarted');
    this.initializeCustomSteps();
  }

  onTimerUpdate(event: { stepIndex: number; elapsedTime: number }): void {
    console.log('Timer update:', event);
  }

  // Demo actions
  simulateStepCompletion(): void {
    if (this.currentStepIndex < this.customSteps.length - 1) {
      this.customSteps[this.currentStepIndex].status = 'completed';
      this.customSteps[this.currentStepIndex].completionTime = Math.floor(Math.random() * 30) + 10;
      this.customSteps[this.currentStepIndex].timerActive = false;

      this.currentStepIndex++;
      if (this.currentStepIndex < this.customSteps.length) {
        this.customSteps[this.currentStepIndex].status = 'active';
        this.customSteps[this.currentStepIndex].timerActive = true;
        this.customSteps[this.currentStepIndex].startTime = Date.now();
      } else {
        this.customStatus = 'COMPLETED';
      }
    }
  }

  simulateStepFailure(): void {
    if (this.currentStepIndex < this.customSteps.length) {
      this.customSteps[this.currentStepIndex].status = 'failed';
      this.customSteps[this.currentStepIndex].timerActive = false;
      this.customSteps[this.currentStepIndex].retryCount = 1;
      this.customStatus = 'FAILED';
    }
  }
}
