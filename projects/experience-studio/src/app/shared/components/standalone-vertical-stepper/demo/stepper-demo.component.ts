import { Component, ChangeDetectionStrategy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { StandaloneVerticalStepperComponent } from '../standalone-vertical-stepper.component';

@Component({
  selector: 'app-stepper-demo',
  standalone: true,
  imports: [CommonModule, StandaloneVerticalStepperComponent],
  templateUrl: './stepper-demo.component.html',
  styleUrls: ['./stepper-demo.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class StepperDemoComponent {
  currentTheme: 'light' | 'dark' = 'light';
  currentMode: 'completed' | 'in-progress' | 'failed' = 'completed';
  showRestartButton = false;

  toggleTheme(): void {
    this.currentTheme = this.currentTheme === 'light' ? 'dark' : 'light';
  }

  setMode(mode: 'completed' | 'in-progress' | 'failed'): void {
    this.currentMode = mode;
  }

  toggleRestartButton(): void {
    this.showRestartButton = !this.showRestartButton;
  }
}
