# Vertical Stepper Component - Usage Examples

This document provides comprehensive examples of how to use the reusable vertical stepper component in different scenarios.

## Table of Contents

1. [Basic Usage](#basic-usage)
2. [Advanced Configuration](#advanced-configuration)
3. [Event Handling](#event-handling)
4. [Dynamic Step Management](#dynamic-step-management)
5. [Custom Styling](#custom-styling)
6. [Integration Patterns](#integration-patterns)

## Basic Usage

### Simple Stepper with Static Data

```typescript
import { Component } from '@angular/core';
import { StandaloneVerticalStepperComponent, StepperItem } from './standalone-vertical-stepper.component';

@Component({
  selector: 'app-simple-stepper',
  standalone: true,
  imports: [StandaloneVerticalStepperComponent],
  template: `
    <app-standalone-vertical-stepper
      [steps]="steps"
      [theme]="'light'">
    </app-standalone-vertical-stepper>
  `
})
export class SimpleStepperComponent {
  steps: StepperItem[] = [
    {
      id: 'step-1',
      title: 'Account Setup',
      description: 'Create your account and verify your email address.',
      status: 'completed',
      completionTime: 30
    },
    {
      id: 'step-2',
      title: 'Profile Information',
      description: 'Add your personal information and preferences.',
      status: 'completed',
      completionTime: 45
    },
    {
      id: 'step-3',
      title: 'Payment Setup',
      description: 'Configure your payment method and billing information.',
      status: 'active',
      timerActive: true,
      startTime: Date.now()
    },
    {
      id: 'step-4',
      title: 'Welcome',
      description: 'Complete your onboarding and start using the platform.',
      status: 'pending'
    }
  ];
}
```

### Demo Mode (Backward Compatibility)

```typescript
@Component({
  selector: 'app-demo-stepper',
  template: `
    <app-standalone-vertical-stepper
      [demoMode]="'in-progress'"
      [theme]="'dark'"
      [restartable]="true">
    </app-standalone-vertical-stepper>
  `
})
export class DemoStepperComponent {}
```

## Advanced Configuration

### Fully Configured Stepper

```typescript
import { Component } from '@angular/core';
import { 
  StandaloneVerticalStepperComponent, 
  StepperItem, 
  StepperConfig,
  StepClickEvent,
  StepStatusChangeEvent
} from './standalone-vertical-stepper.component';

@Component({
  selector: 'app-advanced-stepper',
  standalone: true,
  imports: [StandaloneVerticalStepperComponent],
  template: `
    <app-standalone-vertical-stepper
      [steps]="steps"
      [theme]="currentTheme"
      [status]="stepperStatus"
      [config]="stepperConfig"
      [currentStepIndex]="activeStepIndex"
      [restartable]="true"
      (stepClick)="handleStepClick($event)"
      (statusChange)="handleStatusChange($event)"
      (restart)="handleRestart()"
      (timerUpdate)="handleTimerUpdate($event)">
    </app-standalone-vertical-stepper>
  `
})
export class AdvancedStepperComponent {
  currentTheme: 'light' | 'dark' = 'light';
  stepperStatus: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'FAILED' = 'IN_PROGRESS';
  activeStepIndex = 1;

  stepperConfig: Partial<StepperConfig> = {
    animationSpeed: 'fast',
    showTimers: true,
    showRetryButtons: true,
    enableCollapse: true,
    autoCollapseCompleted: true,
    enableTypewriter: true,
    showStepNumbers: true,
    showConnectingLines: true,
    customClasses: ['my-custom-stepper', 'production-mode']
  };

  steps: StepperItem[] = [
    {
      id: 'data-validation',
      title: 'Data Validation',
      description: '**Validating Input Data**\n\nChecking data integrity and format:\n- Schema validation\n- Data type verification\n- Required field checks',
      status: 'completed',
      completionTime: 12,
      collapsible: true
    },
    {
      id: 'processing',
      title: 'Data Processing',
      description: '**Processing Your Data**\n\nApplying transformations and calculations:\n- Data normalization\n- Business rule application\n- Quality checks',
      status: 'active',
      timerActive: true,
      startTime: Date.now(),
      collapsible: false
    },
    {
      id: 'storage',
      title: 'Data Storage',
      description: 'Saving processed data to secure storage systems.',
      status: 'pending',
      collapsible: true
    }
  ];

  handleStepClick(event: StepClickEvent): void {
    console.log(`Step ${event.stepIndex} clicked:`, event.action);
    
    if (event.action === 'retry') {
      this.retryStep(event.stepIndex);
    }
  }

  handleStatusChange(event: StepStatusChangeEvent): void {
    console.log(`Step ${event.stepIndex} status changed from ${event.previousStatus} to ${event.newStatus}`);
  }

  handleRestart(): void {
    this.activeStepIndex = 0;
    this.stepperStatus = 'PENDING';
    this.resetAllSteps();
  }

  handleTimerUpdate(event: { stepIndex: number; elapsedTime: number }): void {
    console.log(`Step ${event.stepIndex} timer: ${event.elapsedTime}s`);
  }

  private retryStep(stepIndex: number): void {
    const step = this.steps[stepIndex];
    if (step) {
      step.status = 'active';
      step.timerActive = true;
      step.startTime = Date.now();
      step.retryCount = (step.retryCount || 0) + 1;
    }
  }

  private resetAllSteps(): void {
    this.steps.forEach(step => {
      step.status = 'pending';
      step.timerActive = false;
      step.retryCount = 0;
      delete step.completionTime;
      delete step.elapsedTime;
    });
  }
}
```

## Event Handling

### Comprehensive Event Management

```typescript
@Component({
  selector: 'app-event-stepper',
  template: `
    <app-standalone-vertical-stepper
      [steps]="steps"
      (stepClick)="onStepClick($event)"
      (statusChange)="onStatusChange($event)"
      (restart)="onRestart()"
      (timerUpdate)="onTimerUpdate($event)">
    </app-standalone-vertical-stepper>
  `
})
export class EventStepperComponent {
  steps: StepperItem[] = [...]; // Your step data

  onStepClick(event: StepClickEvent): void {
    switch (event.action) {
      case 'toggle':
        this.handleStepToggle(event);
        break;
      case 'retry':
        this.handleStepRetry(event);
        break;
    }
  }

  onStatusChange(event: StepStatusChangeEvent): void {
    // Log status changes for analytics
    this.analyticsService.trackStepStatusChange({
      stepId: event.step.id,
      fromStatus: event.previousStatus,
      toStatus: event.newStatus,
      timestamp: Date.now()
    });

    // Handle specific status transitions
    if (event.newStatus === 'completed') {
      this.handleStepCompletion(event);
    } else if (event.newStatus === 'failed') {
      this.handleStepFailure(event);
    }
  }

  onRestart(): void {
    // Confirm restart action
    if (confirm('Are you sure you want to restart the process?')) {
      this.restartProcess();
    }
  }

  onTimerUpdate(event: { stepIndex: number; elapsedTime: number }): void {
    // Update progress indicators or send heartbeat
    this.updateProgressIndicator(event.stepIndex, event.elapsedTime);
    
    // Send periodic updates to backend
    if (event.elapsedTime % 30 === 0) { // Every 30 seconds
      this.sendProgressUpdate(event);
    }
  }

  private handleStepToggle(event: StepClickEvent): void {
    console.log(`Toggling step: ${event.step.title}`);
  }

  private handleStepRetry(event: StepClickEvent): void {
    console.log(`Retrying step: ${event.step.title}`);
    // Implement retry logic
  }

  private handleStepCompletion(event: StepStatusChangeEvent): void {
    // Move to next step or complete process
    const nextStepIndex = event.stepIndex + 1;
    if (nextStepIndex < this.steps.length) {
      this.activateStep(nextStepIndex);
    } else {
      this.completeProcess();
    }
  }

  private handleStepFailure(event: StepStatusChangeEvent): void {
    // Handle failure scenarios
    this.notificationService.showError(`Step failed: ${event.step.title}`);
  }
}
```

## Dynamic Step Management

### Adding and Removing Steps Dynamically

```typescript
@Component({
  selector: 'app-dynamic-stepper',
  template: `
    <div class="controls">
      <button (click)="addStep()">Add Step</button>
      <button (click)="removeLastStep()">Remove Last Step</button>
      <button (click)="updateStepStatus(1, 'completed')">Complete Step 2</button>
    </div>
    
    <app-standalone-vertical-stepper
      [steps]="steps"
      [currentStepIndex]="currentStep"
      (stepClick)="onStepClick($event)">
    </app-standalone-vertical-stepper>
  `
})
export class DynamicStepperComponent {
  steps: StepperItem[] = [
    {
      id: 'initial-step',
      title: 'Initial Step',
      description: 'This is the starting step.',
      status: 'completed'
    }
  ];
  
  currentStep = 0;
  private stepCounter = 1;

  addStep(): void {
    this.stepCounter++;
    const newStep: StepperItem = {
      id: `dynamic-step-${this.stepCounter}`,
      title: `Dynamic Step ${this.stepCounter}`,
      description: `This step was added dynamically at ${new Date().toLocaleTimeString()}.`,
      status: 'pending'
    };
    
    this.steps = [...this.steps, newStep];
  }

  removeLastStep(): void {
    if (this.steps.length > 1) {
      this.steps = this.steps.slice(0, -1);
      
      // Adjust current step if necessary
      if (this.currentStep >= this.steps.length) {
        this.currentStep = this.steps.length - 1;
      }
    }
  }

  updateStepStatus(stepIndex: number, newStatus: 'pending' | 'active' | 'completed' | 'failed'): void {
    if (stepIndex >= 0 && stepIndex < this.steps.length) {
      const updatedSteps = [...this.steps];
      updatedSteps[stepIndex] = {
        ...updatedSteps[stepIndex],
        status: newStatus,
        completionTime: newStatus === 'completed' ? Math.floor(Math.random() * 60) + 10 : undefined
      };
      this.steps = updatedSteps;
    }
  }

  onStepClick(event: StepClickEvent): void {
    console.log('Dynamic step clicked:', event);
  }
}
```
