import { Injectable, inject, DestroyRef } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { createLogger } from '../../../utils/logger';

export interface NodePosition {
  x: number;
  y: number;
}

export interface NodeDimensions {
  width: number;
  height: number;
}

export interface NodeBounds {
  x: number;
  y: number;
  width: number;
  height: number;
}

export interface PositioningConfig {
  gridSpacing: { x: number; y: number };
  columnsPerRow: number;
  defaultNodeSize: NodeDimensions;
  adjacentSpacing: { x: number; y: number };
  minDistanceFromSelected: number;
}

export interface PositioningStrategy {
  type: 'grid' | 'adjacent' | 'fallback' | 'flow';
  description: string;
}

export interface PositionCalculationResult {
  positions: NodePosition[];
  strategy: PositioningStrategy;
  metadata: {
    selectedNodePosition?: NodePosition;
    availableSpace: NodeBounds;
    totalNodes: number;
    newNodesCount: number;
  };
}

@Injectable({
  providedIn: 'root'
})
export class UIDesignNodePositioningService {
  private readonly logger = createLogger('UIDesignNodePositioningService');
  private readonly destroyRef = inject(DestroyRef);

  private readonly defaultConfig: PositioningConfig = {
    gridSpacing: { x: 480, y: 780 },
    columnsPerRow: 2,
    defaultNodeSize: { width: 420, height: 720 },
    adjacentSpacing: { x: 500, y: 800 },
    minDistanceFromSelected: 50
  };

  private readonly config$ = new BehaviorSubject<PositioningConfig>(this.defaultConfig);

  private readonly canvasBounds$ = new BehaviorSubject<NodeBounds>({
    x: -2000,
    y: -2000,
    width: 4000,
    height: 4000
  });

  constructor() {
  }

  getConfig(): PositioningConfig {
    return this.config$.value;
  }

  updateConfig(updates: Partial<PositioningConfig>): void {
    const current = this.config$.value;
    const updated = { ...current, ...updates };
    this.config$.next(updated);
  }

  calculateInitialGenerationPositions(
    nodeCount: number,
    existingNodes: Array<{ id: string; position: NodePosition; data: { width: number; height: number } }> = []
  ): PositionCalculationResult {

    const config = this.config$.value;

    if (existingNodes.length > 0) {
      return this.calculateIntelligentPositions(nodeCount, existingNodes, 'flow');
    }

    const positions: NodePosition[] = [];
    for (let i = 0; i < nodeCount; i++) {
      const position = this.calculateGridPosition(i, nodeCount, config);
      positions.push(position);
    }

    const result: PositionCalculationResult = {
      positions,
      strategy: {
        type: 'grid',
        description: 'Centered grid layout for initial generation'
      },
      metadata: {
        availableSpace: this.canvasBounds$.value,
        totalNodes: nodeCount,
        newNodesCount: nodeCount
      }
    };

    return result;
  }

  calculateRegenerationPositions(
    existingNodes: Array<{ id: string; position: NodePosition; dimensions?: NodeDimensions }>,
    selectedNodeId: string,
    newNodesCount: number
  ): PositionCalculationResult {

    const standardNodes = existingNodes.map(node => ({
      id: node.id,
      position: node.position,
      data: {
        width: node.dimensions?.width || this.defaultConfig.defaultNodeSize.width,
        height: node.dimensions?.height || this.defaultConfig.defaultNodeSize.height
      }
    }));

    const selectedNode = standardNodes.find(node => node.id === selectedNodeId);
    if (!selectedNode) {
      return this.calculateIntelligentPositions(newNodesCount, standardNodes, 'flow');
    }

    const adjacentResult = this.tryIntelligentAdjacentPositioning(standardNodes, selectedNode, newNodesCount);
    if (adjacentResult) {
      return adjacentResult;
    }

    return this.calculateIntelligentPositions(newNodesCount, standardNodes, 'flow');
  }

  private calculateIntelligentPositions(
    newNodesCount: number,
    existingNodes: Array<{ id: string; position: NodePosition; data: { width: number; height: number } }>,
    strategy: 'grid' | 'flow' | 'adjacent' = 'flow'
  ): PositionCalculationResult {

    const positions = this.findOptimalPositions(newNodesCount, existingNodes, strategy);

    const config = this.config$.value;
    const verifiedPositions: NodePosition[] = [];

    for (let i = 0; i < positions.length; i++) {
      const position = positions[i];
      const collision = this.checkCollision(
        position,
        config.defaultNodeSize,
        existingNodes,
        config.minDistanceFromSelected
      );

      if (!collision.hasCollision) {
        verifiedPositions.push(position);
      } else {

        const alternative = this.findAlternativePosition(position, existingNodes, config);
        verifiedPositions.push(alternative);
      }
    }

    const result: PositionCalculationResult = {
      positions: verifiedPositions,
      strategy: {
        type: strategy,
        description: `Intelligent ${strategy} positioning with collision avoidance`
      },
      metadata: {
        availableSpace: this.canvasBounds$.value,
        totalNodes: existingNodes.length + newNodesCount,
        newNodesCount
      }
    };

    return result;
  }

  private calculateGridPosition(nodeIndex: number, totalNodes: number, config: PositioningConfig): NodePosition {
    const row = Math.floor(nodeIndex / config.columnsPerRow);
    const col = nodeIndex % config.columnsPerRow;

    const totalCols = Math.min(totalNodes, config.columnsPerRow);
    const gridWidth = totalCols * config.gridSpacing.x;
    const startX = -gridWidth / 2 + config.gridSpacing.x / 2;

    const totalRows = Math.ceil(totalNodes / config.columnsPerRow);
    const gridHeight = totalRows * config.gridSpacing.y;
    const startY = -gridHeight / 2 + config.gridSpacing.y / 2;

    return {
      x: startX + col * config.gridSpacing.x,
      y: startY + row * config.gridSpacing.y
    };
  }

  private tryIntelligentAdjacentPositioning(
    existingNodes: Array<{ id: string; position: NodePosition; data: { width: number; height: number } }>,
    selectedNode: { id: string; position: NodePosition; data: { width: number; height: number } },
    newNodesCount: number
  ): PositionCalculationResult | null {

    const config = this.config$.value;
    const positions: NodePosition[] = [];

    const adjacentOffsets = [
      { x: selectedNode.data.width + config.minDistanceFromSelected, y: 0 },
      { x: 0, y: selectedNode.data.height + config.minDistanceFromSelected },
      { x: -(config.defaultNodeSize.width + config.minDistanceFromSelected), y: 0 },
      { x: 0, y: -(config.defaultNodeSize.height + config.minDistanceFromSelected) }
    ];

    for (const offset of adjacentOffsets) {
      const basePosition = {
        x: selectedNode.position.x + offset.x,
        y: selectedNode.position.y + offset.y
      };

      const candidatePositions = this.generateAdjacentSequence(
        basePosition,
        newNodesCount,
        existingNodes,
        config
      );

      if (candidatePositions.length === newNodesCount) {
        positions.push(...candidatePositions);
        break;
      }
    }

    if (positions.length < newNodesCount) {
      return null;
    }

    const result: PositionCalculationResult = {
      positions,
      strategy: {
        type: 'adjacent',
        description: 'Intelligent adjacent placement with collision avoidance'
      },
      metadata: {
        selectedNodePosition: selectedNode.position,
        availableSpace: this.canvasBounds$.value,
        totalNodes: existingNodes.length + newNodesCount,
        newNodesCount
      }
    };

    return result;
  }

  private generateAdjacentSequence(
    startPosition: NodePosition,
    count: number,
    existingNodes: Array<{ position: NodePosition; data: { width: number; height: number } }>,
    config: PositioningConfig
  ): NodePosition[] {
    const positions: NodePosition[] = [];
    const spacing = config.defaultNodeSize.width + config.minDistanceFromSelected;

    for (let i = 0; i < count; i++) {
      const position = {
        x: startPosition.x + (i * spacing),
        y: startPosition.y
      };

      const collision = this.checkCollision(
        position,
        config.defaultNodeSize,
        existingNodes,
        config.minDistanceFromSelected
      );

      if (collision.hasCollision) {

        break;
      }

      if (!this.isPositionWithinBounds(position, this.canvasBounds$.value)) {
        break;
      }

      positions.push(position);
    }

    return positions;
  }

  private findAlternativePosition(
    originalPosition: NodePosition,
    existingNodes: Array<{ position: NodePosition; data: { width: number; height: number } }>,
    config: PositioningConfig
  ): NodePosition {
    const searchRadius = config.defaultNodeSize.width + config.minDistanceFromSelected;
    const searchSteps = 8;

    for (let radius = searchRadius; radius <= searchRadius * 3; radius += searchRadius) {
      for (let step = 0; step < searchSteps; step++) {
        const angle = (step / searchSteps) * 2 * Math.PI;
        const candidatePosition = {
          x: originalPosition.x + Math.cos(angle) * radius,
          y: originalPosition.y + Math.sin(angle) * radius
        };

        const collision = this.checkCollision(
          candidatePosition,
          config.defaultNodeSize,
          existingNodes,
          config.minDistanceFromSelected
        );

        if (!collision.hasCollision && this.isPositionWithinBounds(candidatePosition, this.canvasBounds$.value)) {
          return candidatePosition;
        }
      }
    }

    return this.calculateSafeFallbackPosition(existingNodes, config);
  }

  private calculateSafeFallbackPosition(
    existingNodes: Array<{ position: NodePosition; data: { width: number; height: number } }>,
    config: PositioningConfig
  ): NodePosition {
    if (existingNodes.length === 0) {
      return { x: 0, y: 0 };
    }

    const rightmostNode = existingNodes.reduce((max, node) =>
      node.position.x + node.data.width > max.position.x + max.data.width ? node : max
    );

    return {
      x: rightmostNode.position.x + rightmostNode.data.width + config.minDistanceFromSelected,
      y: rightmostNode.position.y
    };
  }

  private getAvailableAdjacentPositions(
    basePosition: NodePosition,
    existingNodes: Array<{ position: NodePosition }>,
    maxNodes: number,
    config: PositioningConfig
  ): NodePosition[] {
    const positions: NodePosition[] = [];
    const canvasBounds = this.canvasBounds$.value;

    for (let i = 0; i < maxNodes; i++) {
      const position = {
        x: basePosition.x + (i * config.adjacentSpacing.x),
        y: basePosition.y
      };

      if (!this.isPositionWithinBounds(position, canvasBounds)) {
        break;
      }

      if (this.hasPositionConflict(position, existingNodes, config)) {
        break;
      }

      positions.push(position);
    }

    return positions;
  }

  private calculateFallbackPositions(
    existingNodes: Array<{ position: NodePosition }>,
    newNodesCount: number
  ): PositionCalculationResult {
    const config = this.config$.value;
    const totalNodes = existingNodes.length + newNodesCount;
    const positions: NodePosition[] = [];

    for (let i = existingNodes.length; i < totalNodes; i++) {
      const position = this.calculateGridPosition(i, totalNodes, config);
      positions.push(position);
    }

    const result: PositionCalculationResult = {
      positions,
      strategy: {
        type: 'fallback',
        description: 'Grid layout continuation after existing nodes'
      },
      metadata: {
        availableSpace: this.canvasBounds$.value,
        totalNodes,
        newNodesCount
      }
    };

    return result;
  }

  private isPositionWithinBounds(position: NodePosition, bounds: NodeBounds): boolean {
    const config = this.config$.value;
    return (
      position.x >= bounds.x &&
      position.y >= bounds.y &&
      position.x + config.defaultNodeSize.width <= bounds.x + bounds.width &&
      position.y + config.defaultNodeSize.height <= bounds.y + bounds.height
    );
  }

  private hasPositionConflict(
    position: NodePosition,
    existingNodes: Array<{ position: NodePosition }>,
    config: PositioningConfig
  ): boolean {
    const nodeSize = config.defaultNodeSize;
    const minDistance = config.minDistanceFromSelected;

    return existingNodes.some(existingNode => {
      const distance = Math.sqrt(
        Math.pow(position.x - existingNode.position.x, 2) +
        Math.pow(position.y - existingNode.position.y, 2)
      );
      return distance < minDistance + Math.max(nodeSize.width, nodeSize.height);
    });
  }

  updateCanvasBounds(bounds: NodeBounds): void {
    this.canvasBounds$.next(bounds);
  }

  getCanvasBounds(): NodeBounds {
    return this.canvasBounds$.value;
  }

  calculateOptimalViewport(allNodePositions: NodePosition[]): { x: number; y: number; zoom: number } {
    if (allNodePositions.length === 0) {
      return { x: 0, y: 0, zoom: 0.5 };
    }

    const config = this.config$.value;

    const minX = Math.min(...allNodePositions.map(p => p.x));
    const maxX = Math.max(...allNodePositions.map(p => p.x + config.defaultNodeSize.width));
    const minY = Math.min(...allNodePositions.map(p => p.y));
    const maxY = Math.max(...allNodePositions.map(p => p.y + config.defaultNodeSize.height));

    const centerX = (minX + maxX) / 2;
    const centerY = (minY + maxY) / 2;

    const contentWidth = maxX - minX;
    const contentHeight = maxY - minY;
    const canvasBounds = this.canvasBounds$.value;

    const zoomX = canvasBounds.width / (contentWidth + 200);
    const zoomY = canvasBounds.height / (contentHeight + 200);
    const zoom = Math.min(Math.max(Math.min(zoomX, zoomY), 0.1), 1.0);

    return {
      x: centerX,
      y: centerY,
      zoom
    };
  }

  private findOptimalPositions(
    newNodesCount: number,
    existingNodes: Array<{ id: string; position: NodePosition; data: { width: number; height: number } }>,
    strategy: 'grid' | 'flow' | 'adjacent' = 'flow'
  ): NodePosition[] {
    const config = this.config$.value;
    const positions: NodePosition[] = [];

    if (strategy === 'grid') {

      for (let i = 0; i < newNodesCount; i++) {
        positions.push(this.calculateGridPosition(i, newNodesCount, config));
      }
    } else if (strategy === 'flow') {

      const startX = existingNodes.length > 0 ?
        Math.max(...existingNodes.map(n => n.position.x + n.data.width)) + config.minDistanceFromSelected : 0;

      for (let i = 0; i < newNodesCount; i++) {
        positions.push({
          x: startX + (i * (config.defaultNodeSize.width + config.minDistanceFromSelected)),
          y: 0
        });
      }
    } else {

      const basePosition = existingNodes.length > 0 ? existingNodes[0].position : { x: 0, y: 0 };
      for (let i = 0; i < newNodesCount; i++) {
        positions.push({
          x: basePosition.x + ((i + 1) * (config.defaultNodeSize.width + config.minDistanceFromSelected)),
          y: basePosition.y
        });
      }
    }

    return positions;
  }

  private checkCollision(
    position: NodePosition,
    dimensions: NodeDimensions,
    existingNodes: Array<{ position: NodePosition; data: { width: number; height: number } }>,
    minDistance: number
  ): { hasCollision: boolean; conflictingNodes: string[] } {
    const conflictingNodes: string[] = [];

    for (let i = 0; i < existingNodes.length; i++) {
      const node = existingNodes[i];

      const rect1 = {
        x: position.x,
        y: position.y,
        width: dimensions.width,
        height: dimensions.height
      };

      const rect2 = {
        x: node.position.x - minDistance,
        y: node.position.y - minDistance,
        width: node.data.width + (minDistance * 2),
        height: node.data.height + (minDistance * 2)
      };

      if (this.rectanglesOverlap(rect1, rect2)) {
        conflictingNodes.push(`node-${i}`);
      }
    }

    return {
      hasCollision: conflictingNodes.length > 0,
      conflictingNodes
    };
  }

  private rectanglesOverlap(
    rect1: { x: number; y: number; width: number; height: number },
    rect2: { x: number; y: number; width: number; height: number }
  ): boolean {
    return !(rect1.x + rect1.width < rect2.x ||
             rect2.x + rect2.width < rect1.x ||
             rect1.y + rect1.height < rect2.y ||
             rect2.y + rect2.height < rect1.y);
  }
}
