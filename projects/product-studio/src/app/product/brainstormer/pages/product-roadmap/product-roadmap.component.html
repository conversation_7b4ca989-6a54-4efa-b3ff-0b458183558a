<div class="roadmap-container">
  <!-- <h1>Project Timeline - Nap App Development</h1> -->
  <div class="roadmap-nav">
    <!-- Left Section: Dropdown + Date Picker -->
    <div class="left-section d-flex align-items-center gap-3">
      <div class="timeline-dropdown">
        <awe-dropdown
          class="d-flex align-self-start"
          selectedValue="Quarter wise"
          [options]="[
            { name: 'Quarter wise', value: 'quarter-wise' },
            { name: 'Month wise', value: 'month-wise' },
            { name: 'Day wise', value: 'day-wise' },
          ]"
          animation="rotateX"
          theme="light"
        ></awe-dropdown>
      </div>

      <div class="date-selection">
        <awe-datepicker
          class="d-flex align-self-start"
          [range]="true"
          [size]="'large'"
          (dateRangeSelected)="onRangeSelected($event)"
        ></awe-datepicker>
      </div>
      <div class="nav-btn d-flex align-items-center gap-3">
        <button class="nav-arrow p-4">
          <awe-icons class="next-icon" iconName="awe_chevron_left"></awe-icons>
        </button>
        <button class="nav-arrow p-4">
          <awe-icons class="next-icon" iconName="awe_chevron_right"></awe-icons>
        </button>
      </div>
    </div>

    <!-- Center Section: Navigation Arrows -->

    <!-- Right Section: View Toggle + Add Task -->
    <div class="right-container d-flex align-items-center gap-3 m-4">
      <div class="roadmap-view">
        <div class="toggle-view d-flex align-items-center gap-4 px-4 py-2">
          <button
            class="btn-toggle"
            [class.active]="activeView === 'timeline'"
            (click)="setActiveView('timeline')"
            title="Timeline View"
          >
            <awe-icons iconName="awe_performance_ metrics"></awe-icons>
          </button>

          <button
            class="btn-toggle"
            [class.active]="activeView === 'card'"
            (click)="setActiveView('card')"
            title="Card View"
          >
            <awe-icons iconName="awe_dock_to_right"></awe-icons>
          </button>
        </div>
      </div>

      <div class="add-task-btn">
        <button class="" (click)="addNewTask()">New Task +</button>
      </div>
    </div>
  </div>

  <!-- Timeline View - Gantt Chart -->
  <app-gantt-chart
    *ngIf="activeView === 'timeline'"
    [tasks]="projectTasks"
    [year]="2025"
  >
  </app-gantt-chart>

  <!-- Card View - Roadmap Cards -->
  <app-product-roadmap-card-view
    *ngIf="activeView === 'card'"
    (addTaskRequested)="addNewTask()">
  </app-product-roadmap-card-view>
</div>

<!-- Edit/Add Task Modal -->
<awe-modal
  [isOpen]="isEditModalOpen"
  (closed)="closeEditModal()"
  [showHeader]="true"
  [showFooter]="true"
  width="600px"
  height="auto"
  position="center"
  animation="fade"
  [showCloseButton]="true"
  modalClass="edit-roadmap-modal"
>
  <!-- Projected Modal Header -->
  <div awe-modal-header class="edit-modal-header">
    <awe-heading variant="s1" type="bold" class="modal-title mb-0">
      {{ isAddingNewTask ? "Add New Roadmap Task" : "Edit Roadmap Task" }}
    </awe-heading>
  </div>

  <!-- Projected Modal Body -->
  <div awe-modal-body class="edit-modal-body">
    <!-- Task Title -->
    <div class="col-md-12 inp-container">
      <div class="label">
        <label for="taskTitle">Title:</label>
      </div>
      <div class="input-wrapper">
        <awe-input
          id="taskTitle"
          type="text"
          variant="fluid"
          label="Title:"
          [(ngModel)]="editableTaskTitle"
          placeholder="Enter task title"
          class="w-100"
        ></awe-input>
      </div>
    </div>

    <!-- Task Description -->
     <div class="col-md-12 mt-2">
      <div>
        <label for="name">Description:</label>
      </div>
      <div class="input-wrapper">
        <awe-input
          variant="fluid"
          [expand]="true"
          id="taskDescription"
         [(ngModel)]="editableTaskDescription"
          placeholder="Enter roadmap task description"
          class="w-100"
        ></awe-input>
      </div>
    </div>

    <!-- Priority -->
 <div class="col-md-12 inp-container mt-2">
      <div class="label">
        <label for="taskPriority">Priority:</label>
      </div>
      <div class="input-wrapper">
        <awe-dropdown
          [selectedValue]="editableTaskPriority"
          [options]="priorities"
          animation="rotateX"
          [(ngModel)]="editableTaskPriority"
          theme="light"
        ></awe-dropdown>
      </div>
    </div>

    <!-- Start Date -->
 <div class="col-md-12 inp-container mt-2">
      <div class="label">
        <label for="taskStartDate">Start Date and End date:</label>
      </div>
      <div class="input-wrapper">
        <awe-datepicker
          [size]="'large'"
          [range]="true"
          (dateRangeSelected)="onRangeSelected($event)"
          [(ngModel)]="editableCardStartAndEndDate"
        ></awe-datepicker>
      </div>
    </div>
    <!-- Regenerate Section -->
   <!-- Regenerate Section -->
    <div class="regenerate-section mt-3">
      <awe-heading variant="h6" type="bold">Regenerate with AI</awe-heading>
      <awe-input
        id="regeneratePrompt"
        [expand]="true"
        [(ngModel)]="regeneratePrompt"
        [icons]="['awe_send']"
        variant="fluid"
        placeholder="What would you like to change?"
        (iconClickEvent)="onRegenerate()"
        class="mb-2"
      >
      </awe-input>
    </div>
  </div>

  <!-- Projected Modal Footer -->
  <div awe-modal-footer class="edit-modal-footer d-flex justify-content-between">
    <button type="button" class="btn-cancel px-5 mx-3" (click)="closeEditModal()">
      Cancel
    </button>
    <button type="button" class="btn-delete px-5" (click)="updateTask()">
      {{ isAddingNewTask ? "Add Task" : "Update" }}
    </button>
  </div>
</awe-modal>
